import { prisma } from '@/lib/prisma';

export interface QualityReport {
  overall: number; // 0-100 总体评分
  seo: SEOCheck;
  readability: ReadabilityCheck;
  images: ImageCheck;
  links: LinkCheck;
  duplicates: DuplicateCheck;
  suggestions: string[];
}

export interface SEOCheck {
  score: number;
  titleLength: { score: number; current: number; optimal: string };
  descriptionLength: { score: number; current: number; optimal: string };
  keywordDensity: { score: number; density: number; optimal: string };
  headingStructure: { score: number; issues: string[] };
  metaTags: { score: number; missing: string[] };
}

export interface ReadabilityCheck {
  score: number;
  wordCount: number;
  sentenceLength: { average: number; score: number };
  paragraphLength: { average: number; score: number };
  readingTime: number;
  fleschScore: number;
}

export interface ImageCheck {
  score: number;
  totalImages: number;
  missingAlt: number;
  oversizedImages: number;
  unoptimizedFormats: number;
}

export interface LinkCheck {
  score: number;
  totalLinks: number;
  brokenLinks: number;
  externalLinks: number;
  internalLinks: number;
}

export interface DuplicateCheck {
  score: number;
  titleSimilarity: number;
  contentSimilarity: number;
  duplicateKeywords: string[];
}

export class ContentQualityChecker {
  // 综合质量检查
  async checkContentQuality(articleId: string): Promise<QualityReport> {
    const article = await prisma.article.findUnique({
      where: { id: articleId },
      include: {
        category: true,
        tags: true,
      },
    });

    if (!article) {
      throw new Error('Article not found');
    }

    // 获取英文内容进行检查（可以扩展为多语言）
    const title = article.title.en || Object.values(article.title)[0];
    const content = article.content.en || Object.values(article.content)[0];
    const description = article.excerpt?.en || article.seoDescription?.en;

    const [seo, readability, images, links, duplicates] = await Promise.all([
      this.checkSEO(title, content, description, article),
      this.checkReadability(content),
      this.checkImages(content),
      this.checkLinks(content),
      this.checkDuplicates(article),
    ]);

    const overall = this.calculateOverallScore([seo, readability, images, links, duplicates]);
    const suggestions = this.generateSuggestions([seo, readability, images, links, duplicates]);

    const report: QualityReport = {
      overall,
      seo,
      readability,
      images,
      links,
      duplicates,
      suggestions,
    };

    // 保存质量检查结果
    await prisma.qualityCheck.create({
      data: {
        articleId,
        checkType: 'COMPREHENSIVE',
        score: overall,
        issues: {
          seo: seo.score < 70 ? 'SEO needs improvement' : null,
          readability: readability.score < 70 ? 'Readability needs improvement' : null,
          images: images.score < 70 ? 'Image optimization needed' : null,
          links: links.score < 70 ? 'Link issues found' : null,
          duplicates: duplicates.score < 70 ? 'Duplicate content detected' : null,
        },
        suggestions,
      },
    });

    return report;
  }

  // SEO检查
  private async checkSEO(
    title: string,
    content: string,
    description?: string,
    article?: any
  ): Promise<SEOCheck> {
    const titleLength = this.checkTitleLength(title);
    const descriptionLength = this.checkDescriptionLength(description || '');
    const keywordDensity = this.checkKeywordDensity(content, article?.seoKeywords?.en || []);
    const headingStructure = this.checkHeadingStructure(content);
    const metaTags = this.checkMetaTags(article);

    const score = Math.round(
      (titleLength.score + descriptionLength.score + keywordDensity.score + 
       headingStructure.score + metaTags.score) / 5
    );

    return {
      score,
      titleLength,
      descriptionLength,
      keywordDensity,
      headingStructure,
      metaTags,
    };
  }

  // 可读性检查
  private checkReadability(content: string): ReadabilityCheck {
    const wordCount = this.countWords(content);
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);

    const sentenceLength = {
      average: wordCount / sentences.length,
      score: this.scoreSentenceLength(wordCount / sentences.length),
    };

    const paragraphLength = {
      average: sentences.length / paragraphs.length,
      score: this.scoreParagraphLength(sentences.length / paragraphs.length),
    };

    const readingTime = Math.ceil(wordCount / 200); // 200 words per minute
    const fleschScore = this.calculateFleschScore(content);

    const score = Math.round(
      (sentenceLength.score + paragraphLength.score + this.scoreWordCount(wordCount) + 
       this.scoreFleschScore(fleschScore)) / 4
    );

    return {
      score,
      wordCount,
      sentenceLength,
      paragraphLength,
      readingTime,
      fleschScore,
    };
  }

  // 图片检查
  private checkImages(content: string): ImageCheck {
    const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g;
    const images = [...content.matchAll(imageRegex)];
    
    const totalImages = images.length;
    const missingAlt = images.filter(img => !img[1] || img[1].trim() === '').length;
    
    // 简化的检查，实际应该检查实际文件
    const oversizedImages = 0;
    const unoptimizedFormats = 0;

    const score = totalImages === 0 ? 100 : 
      Math.round(((totalImages - missingAlt) / totalImages) * 100);

    return {
      score,
      totalImages,
      missingAlt,
      oversizedImages,
      unoptimizedFormats,
    };
  }

  // 链接检查
  private checkLinks(content: string): LinkCheck {
    const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
    const links = [...content.matchAll(linkRegex)];
    
    const totalLinks = links.length;
    const externalLinks = links.filter(link => 
      link[2].startsWith('http') && !link[2].includes(process.env.NEXT_PUBLIC_SITE_URL || '')
    ).length;
    const internalLinks = totalLinks - externalLinks;
    
    // 简化的检查，实际应该验证链接有效性
    const brokenLinks = 0;

    const score = totalLinks === 0 ? 100 : 
      Math.round(((totalLinks - brokenLinks) / totalLinks) * 100);

    return {
      score,
      totalLinks,
      brokenLinks,
      externalLinks,
      internalLinks,
    };
  }

  // 重复内容检查
  private async checkDuplicates(article: any): Promise<DuplicateCheck> {
    // 简化的重复检查，实际应该使用更复杂的算法
    const titleSimilarity = 0; // 需要实现标题相似度检查
    const contentSimilarity = 0; // 需要实现内容相似度检查
    const duplicateKeywords: string[] = []; // 需要实现关键词重叠检查

    const score = 100; // 简化评分

    return {
      score,
      titleSimilarity,
      contentSimilarity,
      duplicateKeywords,
    };
  }

  // 辅助方法
  private checkTitleLength(title: string) {
    const length = title.length;
    const optimal = '50-60 characters';
    let score = 100;
    
    if (length < 30 || length > 70) score = 50;
    else if (length < 40 || length > 60) score = 80;

    return { score, current: length, optimal };
  }

  private checkDescriptionLength(description: string) {
    const length = description.length;
    const optimal = '150-160 characters';
    let score = 100;
    
    if (length < 120 || length > 180) score = 50;
    else if (length < 140 || length > 170) score = 80;

    return { score, current: length, optimal };
  }

  private checkKeywordDensity(content: string, keywords: string[]) {
    if (keywords.length === 0) {
      return { score: 50, density: 0, optimal: '1-3%' };
    }

    const wordCount = this.countWords(content);
    const keywordCount = keywords.reduce((count, keyword) => {
      const regex = new RegExp(keyword, 'gi');
      return count + (content.match(regex) || []).length;
    }, 0);

    const density = (keywordCount / wordCount) * 100;
    let score = 100;

    if (density < 0.5 || density > 5) score = 50;
    else if (density < 1 || density > 3) score = 80;

    return { score, density, optimal: '1-3%' };
  }

  private checkHeadingStructure(content: string) {
    const headings = content.match(/^#{1,6}\s+.+$/gm) || [];
    const issues: string[] = [];
    
    if (headings.length === 0) {
      issues.push('No headings found');
    }

    const score = issues.length === 0 ? 100 : 70;
    return { score, issues };
  }

  private checkMetaTags(article: any) {
    const missing: string[] = [];
    
    if (!article.seoTitle) missing.push('SEO Title');
    if (!article.seoDescription) missing.push('SEO Description');
    if (!article.seoKeywords) missing.push('SEO Keywords');

    const score = missing.length === 0 ? 100 : Math.max(0, 100 - (missing.length * 25));
    return { score, missing };
  }

  private countWords(text: string): number {
    return text.split(/\s+/).filter(word => word.length > 0).length;
  }

  private scoreSentenceLength(avgLength: number): number {
    if (avgLength >= 15 && avgLength <= 20) return 100;
    if (avgLength >= 10 && avgLength <= 25) return 80;
    return 60;
  }

  private scoreParagraphLength(avgSentences: number): number {
    if (avgSentences >= 3 && avgSentences <= 5) return 100;
    if (avgSentences >= 2 && avgSentences <= 7) return 80;
    return 60;
  }

  private scoreWordCount(wordCount: number): number {
    if (wordCount >= 300 && wordCount <= 2000) return 100;
    if (wordCount >= 200 && wordCount <= 3000) return 80;
    return 60;
  }

  private calculateFleschScore(content: string): number {
    // 简化的Flesch阅读难度评分
    const sentences = content.split(/[.!?]+/).length;
    const words = this.countWords(content);
    const syllables = words * 1.5; // 简化的音节计算

    return 206.835 - (1.015 * (words / sentences)) - (84.6 * (syllables / words));
  }

  private scoreFleschScore(fleschScore: number): number {
    if (fleschScore >= 60) return 100;
    if (fleschScore >= 30) return 80;
    return 60;
  }

  private calculateOverallScore(checks: Array<{ score: number }>): number {
    const totalScore = checks.reduce((sum, check) => sum + check.score, 0);
    return Math.round(totalScore / checks.length);
  }

  private generateSuggestions(checks: Array<any>): string[] {
    const suggestions: string[] = [];
    
    if (checks[0].score < 70) suggestions.push('Improve SEO optimization');
    if (checks[1].score < 70) suggestions.push('Enhance content readability');
    if (checks[2].score < 70) suggestions.push('Optimize images and add alt text');
    if (checks[3].score < 70) suggestions.push('Fix broken links and improve link structure');
    if (checks[4].score < 70) suggestions.push('Address duplicate content issues');

    return suggestions;
  }
}
