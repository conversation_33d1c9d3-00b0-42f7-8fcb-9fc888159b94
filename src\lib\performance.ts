// Core Web Vitals 监控
export interface WebVitalsMetric {
  name: 'CLS' | 'FID' | 'FCP' | 'LCP' | 'TTFB';
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  id: string;
}

// 性能阈值配置
export const PERFORMANCE_THRESHOLDS = {
  // Largest Contentful Paint (LCP)
  LCP: {
    good: 2500,
    poor: 4000,
  },
  // First Input Delay (FID)
  FID: {
    good: 100,
    poor: 300,
  },
  // Cumulative Layout Shift (CLS)
  CLS: {
    good: 0.1,
    poor: 0.25,
  },
  // First Contentful Paint (FCP)
  FCP: {
    good: 1800,
    poor: 3000,
  },
  // Time to First Byte (TTFB)
  TTFB: {
    good: 800,
    poor: 1800,
  },
} as const;

// 获取性能评级
export function getPerformanceRating(name: WebVitalsMetric['name'], value: number): WebVitalsMetric['rating'] {
  const thresholds = PERFORMANCE_THRESHOLDS[name];
  
  if (value <= thresholds.good) {
    return 'good';
  } else if (value <= thresholds.poor) {
    return 'needs-improvement';
  } else {
    return 'poor';
  }
}

// 性能监控类
export class PerformanceMonitor {
  private metrics: Map<string, WebVitalsMetric> = new Map();
  private observers: Map<string, PerformanceObserver> = new Map();

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeObservers();
    }
  }

  private initializeObservers() {
    // 监控 LCP
    this.observeMetric('largest-contentful-paint', (entries) => {
      const lastEntry = entries[entries.length - 1];
      this.recordMetric('LCP', lastEntry.startTime);
    });

    // 监控 FID
    this.observeMetric('first-input', (entries) => {
      const firstEntry = entries[0];
      this.recordMetric('FID', firstEntry.processingStart - firstEntry.startTime);
    });

    // 监控 CLS
    this.observeMetric('layout-shift', (entries) => {
      let clsValue = 0;
      for (const entry of entries) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      }
      this.recordMetric('CLS', clsValue);
    });

    // 监控 FCP
    this.observeMetric('paint', (entries) => {
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
      if (fcpEntry) {
        this.recordMetric('FCP', fcpEntry.startTime);
      }
    });

    // 监控 TTFB
    this.observeMetric('navigation', (entries) => {
      const navigationEntry = entries[0] as PerformanceNavigationTiming;
      this.recordMetric('TTFB', navigationEntry.responseStart - navigationEntry.requestStart);
    });
  }

  private observeMetric(type: string, callback: (entries: PerformanceEntry[]) => void) {
    try {
      const observer = new PerformanceObserver((list) => {
        callback(list.getEntries());
      });
      
      observer.observe({ type, buffered: true });
      this.observers.set(type, observer);
    } catch (error) {
      console.warn(`Failed to observe ${type}:`, error);
    }
  }

  private recordMetric(name: WebVitalsMetric['name'], value: number) {
    const existing = this.metrics.get(name);
    const delta = existing ? value - existing.value : value;
    
    const metric: WebVitalsMetric = {
      name,
      value,
      rating: getPerformanceRating(name, value),
      delta,
      id: this.generateId(),
    };

    this.metrics.set(name, metric);
    this.reportMetric(metric);
  }

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private reportMetric(metric: WebVitalsMetric) {
    // 发送到分析服务
    this.sendToAnalytics(metric);
    
    // 控制台输出（开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${metric.name}: ${metric.value}ms (${metric.rating})`);
    }
  }

  private sendToAnalytics(metric: WebVitalsMetric) {
    // 这里可以集成各种分析服务
    // 例如：Google Analytics, Sentry, 自定义分析服务等
    
    // Google Analytics 4 示例
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', metric.name, {
        event_category: 'Web Vitals',
        value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
        event_label: metric.id,
        non_interaction: true,
      });
    }

    // 自定义分析服务
    if (typeof window !== 'undefined') {
      fetch('/api/analytics/performance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          metric,
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: Date.now(),
        }),
      }).catch(error => {
        console.warn('Failed to send performance metric:', error);
      });
    }
  }

  // 获取所有指标
  getMetrics(): WebVitalsMetric[] {
    return Array.from(this.metrics.values());
  }

  // 获取特定指标
  getMetric(name: WebVitalsMetric['name']): WebVitalsMetric | undefined {
    return this.metrics.get(name);
  }

  // 清理观察器
  disconnect() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
  }
}

// 全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

// 页面加载性能监控
export function measurePageLoad() {
  if (typeof window === 'undefined') return;

  window.addEventListener('load', () => {
    setTimeout(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      const metrics = {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        totalTime: navigation.loadEventEnd - navigation.fetchStart,
        dnsLookup: navigation.domainLookupEnd - navigation.domainLookupStart,
        tcpConnect: navigation.connectEnd - navigation.connectStart,
        request: navigation.responseEnd - navigation.requestStart,
        response: navigation.responseEnd - navigation.responseStart,
        domProcessing: navigation.domComplete - navigation.domLoading,
      };

      console.log('[Page Load Metrics]', metrics);
      
      // 发送到分析服务
      fetch('/api/analytics/page-load', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          metrics,
          url: window.location.href,
          timestamp: Date.now(),
        }),
      }).catch(error => {
        console.warn('Failed to send page load metrics:', error);
      });
    }, 0);
  });
}

// 资源加载性能监控
export function measureResourceLoading() {
  if (typeof window === 'undefined') return;

  const observer = new PerformanceObserver((list) => {
    const entries = list.getEntries();
    
    entries.forEach((entry) => {
      if (entry.entryType === 'resource') {
        const resource = entry as PerformanceResourceTiming;
        
        // 监控慢加载的资源
        if (resource.duration > 1000) {
          console.warn(`[Slow Resource] ${resource.name}: ${resource.duration}ms`);
          
          // 发送警告到监控服务
          fetch('/api/analytics/slow-resource', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              name: resource.name,
              duration: resource.duration,
              size: resource.transferSize,
              type: resource.initiatorType,
              url: window.location.href,
              timestamp: Date.now(),
            }),
          }).catch(() => {
            // 静默失败
          });
        }
      }
    });
  });

  observer.observe({ entryTypes: ['resource'] });
}

// 内存使用监控
export function measureMemoryUsage() {
  if (typeof window === 'undefined' || !(performance as any).memory) return;

  const memory = (performance as any).memory;
  
  const metrics = {
    usedJSHeapSize: memory.usedJSHeapSize,
    totalJSHeapSize: memory.totalJSHeapSize,
    jsHeapSizeLimit: memory.jsHeapSizeLimit,
    usagePercentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
  };

  // 如果内存使用超过80%，发出警告
  if (metrics.usagePercentage > 80) {
    console.warn('[High Memory Usage]', metrics);
  }

  return metrics;
}

// 长任务监控
export function measureLongTasks() {
  if (typeof window === 'undefined') return;

  try {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      
      entries.forEach((entry) => {
        console.warn(`[Long Task] Duration: ${entry.duration}ms, Start: ${entry.startTime}ms`);
        
        // 发送长任务信息到监控服务
        fetch('/api/analytics/long-task', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            duration: entry.duration,
            startTime: entry.startTime,
            url: window.location.href,
            timestamp: Date.now(),
          }),
        }).catch(() => {
          // 静默失败
        });
      });
    });

    observer.observe({ entryTypes: ['longtask'] });
  } catch (error) {
    console.warn('Long task monitoring not supported:', error);
  }
}

// 初始化所有性能监控
export function initializePerformanceMonitoring() {
  if (typeof window === 'undefined') return;

  measurePageLoad();
  measureResourceLoading();
  measureLongTasks();
  
  // 定期检查内存使用
  setInterval(() => {
    measureMemoryUsage();
  }, 30000); // 每30秒检查一次
}

// 性能报告生成
export function generatePerformanceReport(): {
  webVitals: WebVitalsMetric[];
  memory?: any;
  timestamp: number;
} {
  return {
    webVitals: performanceMonitor.getMetrics(),
    memory: measureMemoryUsage(),
    timestamp: Date.now(),
  };
}
