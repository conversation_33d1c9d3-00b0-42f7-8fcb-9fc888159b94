'use client';

import * as React from 'react';
import { Button } from './button';
import { Card, CardBody, CardHeader } from './card';

export interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  level?: 'page' | 'section' | 'component';
}

export interface ErrorFallbackProps {
  error: Error;
  retry: () => void;
  level?: 'page' | 'section' | 'component';
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundaryClass extends React.Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.props.onError?.(error, errorInfo);
  }

  retry = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError && this.state.error) {
      const Fallback = this.props.fallback || DefaultErrorFallback;
      return (
        <Fallback 
          error={this.state.error} 
          retry={this.retry}
          level={this.props.level}
        />
      );
    }

    return this.props.children;
  }
}

// 默认错误回退组件
const DefaultErrorFallback: React.FC<ErrorFallbackProps> = ({ 
  error, 
  retry, 
  level = 'component' 
}) => {
  const getErrorMessage = () => {
    switch (level) {
      case 'page':
        return '页面加载出现问题';
      case 'section':
        return '此部分内容暂时无法显示';
      case 'component':
      default:
        return '组件加载失败';
    }
  };

  const getErrorDescription = () => {
    if (process.env.NODE_ENV === 'development') {
      return error.message;
    }
    return '请稍后重试，如果问题持续存在，请联系客服。';
  };

  return (
    <Card className="border-destructive/50 bg-destructive/5">
      <CardHeader>
        <div className="flex items-center space-x-2">
          <svg
            className="h-5 w-5 text-destructive"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
          <h3 className="text-lg font-semibold text-destructive">
            {getErrorMessage()}
          </h3>
        </div>
      </CardHeader>
      <CardBody>
        <p className="text-sm text-muted-foreground mb-4">
          {getErrorDescription()}
        </p>
        <Button onClick={retry} variant="outline" size="sm">
          重试
        </Button>
      </CardBody>
    </Card>
  );
};

// 异步错误边界
export const AsyncErrorBoundary: React.FC<{
  children: React.ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
}> = ({ children, fallback }) => {
  const [error, setError] = React.useState<Error | null>(null);

  React.useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      setError(new Error(event.reason));
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  if (error) {
    const Fallback = fallback || DefaultErrorFallback;
    return <Fallback error={error} retry={() => setError(null)} />;
  }

  return <>{children}</>;
};

// 简化的错误边界Hook
export const useErrorBoundary = () => {
  const [error, setError] = React.useState<Error | null>(null);

  const captureError = React.useCallback((error: Error) => {
    setError(error);
  }, []);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  return { captureError, resetError };
};

export { ErrorBoundaryClass as ErrorBoundary, DefaultErrorFallback };
