import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      type,
      locale,
      userId,
      sessionId,
      provider,
      model,
      tokens,
      cost,
      success,
      error,
    } = body;

    // 记录AI请求到数据库
    await prisma.aIRequest.create({
      data: {
        provider: provider || 'unknown',
        model: model || 'unknown',
        prompt: '', // 不记录完整提示词以保护隐私
        response: success ? 'Success' : null,
        tokens: tokens || 0,
        cost: cost || 0,
        userId,
        sessionId,
        type,
        locale,
        success,
        error: error || null,
      },
    });

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('AI log API error:', error);
    
    // 即使日志记录失败也不影响主要功能
    return NextResponse.json(
      { success: false, error: 'Failed to log AI request' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '100');
    const offset = parseInt(searchParams.get('offset') || '0');
    const provider = searchParams.get('provider');
    const type = searchParams.get('type');

    // 构建查询条件
    const where: any = {};
    if (provider) where.provider = provider;
    if (type) where.type = type;

    // 获取AI请求统计
    const [requests, total] = await Promise.all([
      prisma.aIRequest.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
        select: {
          id: true,
          provider: true,
          model: true,
          type: true,
          locale: true,
          tokens: true,
          cost: true,
          success: true,
          createdAt: true,
        },
      }),
      prisma.aIRequest.count({ where }),
    ]);

    return NextResponse.json({
      requests,
      total,
      limit,
      offset,
    });

  } catch (error) {
    console.error('AI log GET API error:', error);
    
    return NextResponse.json(
      { error: 'Failed to fetch AI logs' },
      { status: 500 }
    );
  }
}
