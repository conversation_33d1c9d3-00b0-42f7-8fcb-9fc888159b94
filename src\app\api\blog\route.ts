import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// 查询参数验证
const querySchema = z.object({
  page: z.string().optional().default('1'),
  limit: z.string().optional().default('10'),
  category: z.string().optional(),
  tag: z.string().optional(),
  status: z.enum(['DRAFT', 'PENDING', 'SCHEDULED', 'PUBLISHED', 'ARCHIVED', 'DELETED']).optional(),
  featured: z.string().optional(),
  locale: z.string().optional(),
  search: z.string().optional(),
  sortBy: z.enum(['createdAt', 'publishedAt', 'views', 'title']).optional().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
});

// 创建文章验证
const createArticleSchema = z.object({
  title: z.record(z.string()), // 多语言标题
  content: z.record(z.string()), // 多语言内容
  excerpt: z.record(z.string()).optional(), // 多语言摘要
  coverImage: z.string().optional(),
  categoryId: z.string(),
  tags: z.array(z.string()).optional(),
  status: z.enum(['DRAFT', 'PENDING', 'SCHEDULED', 'PUBLISHED']).optional().default('DRAFT'),
  publishedAt: z.string().optional(),
  scheduledAt: z.string().optional(),
  featured: z.boolean().optional().default(false),
  seoTitle: z.record(z.string()).optional(),
  seoDescription: z.record(z.string()).optional(),
  seoKeywords: z.record(z.array(z.string())).optional(),
  aiGenerated: z.boolean().optional().default(false),
  aiMetadata: z.any().optional(),
});

// GET /api/blog - 获取文章列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = querySchema.parse(Object.fromEntries(searchParams));

    const page = parseInt(query.page);
    const limit = parseInt(query.limit);
    const skip = (page - 1) * limit;

    // 构建查询条件
    const where: any = {};

    if (query.category) {
      where.category = { slug: query.category };
    }

    if (query.tag) {
      where.tags = { some: { slug: query.tag } };
    }

    if (query.status) {
      where.status = query.status;
    }

    if (query.featured) {
      where.featured = query.featured === 'true';
    }

    if (query.search) {
      where.OR = [
        { title: { path: [query.locale || 'en'], string_contains: query.search } },
        { content: { path: [query.locale || 'en'], string_contains: query.search } },
        { excerpt: { path: [query.locale || 'en'], string_contains: query.search } },
      ];
    }

    // 排序
    const orderBy: any = {};
    orderBy[query.sortBy] = query.sortOrder;

    // 查询文章
    const [articles, total] = await Promise.all([
      prisma.article.findMany({
        where,
        include: {
          author: { select: { id: true, name: true, avatar: true } },
          category: { select: { id: true, name: true, slug: true } },
          tags: { select: { id: true, name: true, slug: true } },
          _count: { select: { comments: true } },
        },
        orderBy,
        skip,
        take: limit,
      }),
      prisma.article.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      data: articles,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    });
  } catch (error) {
    console.error('Error fetching articles:', error);
    return NextResponse.json(
      { error: 'Failed to fetch articles' },
      { status: 500 }
    );
  }
}

// POST /api/blog - 创建新文章
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const data = createArticleSchema.parse(body);

    // 获取用户ID（这里需要实现身份验证）
    const authorId = 'user-id'; // 临时硬编码，实际需要从JWT或session获取

    // 生成slug
    const slug = generateSlug(data.title.en || Object.values(data.title)[0]);

    // 计算阅读时间
    const readingTime = calculateReadingTime(data.content.en || Object.values(data.content)[0]);

    // 创建文章
    const article = await prisma.article.create({
      data: {
        slug,
        title: data.title,
        content: data.content,
        excerpt: data.excerpt,
        coverImage: data.coverImage,
        authorId,
        categoryId: data.categoryId,
        status: data.status,
        publishedAt: data.publishedAt ? new Date(data.publishedAt) : undefined,
        scheduledAt: data.scheduledAt ? new Date(data.scheduledAt) : undefined,
        readingTime,
        featured: data.featured,
        seoTitle: data.seoTitle,
        seoDescription: data.seoDescription,
        seoKeywords: data.seoKeywords,
        aiGenerated: data.aiGenerated,
        aiMetadata: data.aiMetadata,
      },
      include: {
        author: { select: { id: true, name: true, avatar: true } },
        category: { select: { id: true, name: true, slug: true } },
        tags: { select: { id: true, name: true, slug: true } },
      },
    });

    // 如果有标签，关联标签
    if (data.tags && data.tags.length > 0) {
      await prisma.article.update({
        where: { id: article.id },
        data: {
          tags: {
            connect: data.tags.map(tagId => ({ id: tagId })),
          },
        },
      });
    }

    return NextResponse.json(article, { status: 201 });
  } catch (error) {
    console.error('Error creating article:', error);
    return NextResponse.json(
      { error: 'Failed to create article' },
      { status: 500 }
    );
  }
}

// 辅助函数
function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

function calculateReadingTime(content: string): number {
  const wordsPerMinute = 200;
  const wordCount = content.split(/\s+/).length;
  return Math.ceil(wordCount / wordsPerMinute);
}
