@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 262 83% 58%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 262 83% 58%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 262 83% 58%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* 多语言字体支持 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&family=Fira+Code:wght@300;400;500&family=Noto+Sans:wght@300;400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;600;700&family=Noto+Sans+JP:wght@300;400;500;600;700&family=Noto+Sans+Arabic:wght@300;400;500;600;700&family=Noto+Sans+Devanagari:wght@300;400;500;600;700&family=Cinzel:wght@400;500;600&display=swap');

:root {
  --font-inter: 'Inter', sans-serif;
  --font-playfair: 'Playfair Display', serif;
  --font-fira-code: 'Fira Code', monospace;
  --font-noto-sans: 'Noto Sans', sans-serif;
  --font-noto-sans-sc: 'Noto Sans SC', sans-serif;
  --font-noto-sans-jp: 'Noto Sans JP', sans-serif;
  --font-noto-sans-arabic: 'Noto Sans Arabic', sans-serif;
  --font-noto-sans-devanagari: 'Noto Sans Devanagari', sans-serif;
  --font-cinzel: 'Cinzel', serif;
}

/* 玄学主题样式 */
.mystical-gradient {
  background: linear-gradient(135deg, 
    hsl(var(--mystical-500)) 0%, 
    hsl(var(--cosmic-600)) 50%, 
    hsl(var(--mystical-700)) 100%);
}

.cosmic-glow {
  box-shadow: 0 0 20px rgba(156, 110, 255, 0.3);
}

.text-gradient {
  background: linear-gradient(135deg, 
    hsl(var(--mystical-600)), 
    hsl(var(--cosmic-500)));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-border rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-accent-foreground/20;
}

/* 选择文本样式 */
::selection {
  @apply bg-primary/20 text-primary-foreground;
}

/* 焦点样式 */
.focus-visible {
  @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
}

/* 动画类 */
.animate-in {
  animation: fade-in 0.5s ease-out;
}

.animate-slide-in {
  animation: slide-in 0.3s ease-out;
}

/* 响应式文字大小 */
.text-responsive-xl {
  @apply text-2xl md:text-3xl lg:text-4xl xl:text-5xl;
}

.text-responsive-lg {
  @apply text-xl md:text-2xl lg:text-3xl;
}

.text-responsive-md {
  @apply text-lg md:text-xl lg:text-2xl;
}

/* 容器样式 */
.container-padding {
  @apply px-4 sm:px-6 lg:px-8;
}

.section-padding {
  @apply py-12 md:py-16 lg:py-20;
}

/* 卡片样式 */
.card-hover {
  @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
}

.mystical-card {
  @apply bg-card/50 backdrop-blur-sm border border-border/50 rounded-lg p-6 card-hover;
}

/* 按钮样式增强 */
.btn-mystical {
  @apply bg-gradient-to-r from-mystical-600 to-cosmic-600 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 hover:shadow-lg hover:scale-105 focus:outline-none focus:ring-2 focus:ring-mystical-500 focus:ring-offset-2;
}

/* 多语言布局支持 */
.rtl {
  direction: rtl;
  text-align: right;
}

.ltr {
  direction: ltr;
  text-align: left;
}

/* 语言特定字体 */
.font-chinese {
  font-family: var(--font-noto-sans-sc), var(--font-inter), sans-serif;
  line-height: 1.7;
  letter-spacing: 0.05em;
}

.font-japanese {
  font-family: var(--font-noto-sans-jp), var(--font-inter), sans-serif;
  line-height: 1.7;
  letter-spacing: 0.05em;
}

.font-arabic {
  font-family: var(--font-noto-sans-arabic), var(--font-inter), sans-serif;
  line-height: 1.8;
  direction: rtl;
  text-align: right;
}

.font-hindi {
  font-family: var(--font-noto-sans-devanagari), var(--font-inter), sans-serif;
  line-height: 1.8;
  letter-spacing: 0.02em;
}

.font-mystical {
  font-family: var(--font-cinzel), var(--font-playfair), serif;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  .mobile-padding {
    @apply px-4 py-2;
  }

  .mobile-text-scale {
    font-size: 110%;
  }

  .mobile-chinese {
    font-size: 120%;
  }

  .mobile-arabic {
    font-size: 115%;
  }

  /* 移动端导航 */
  .mobile-menu {
    @apply fixed inset-y-0 right-0 w-80 bg-background border-l border-border transform transition-transform duration-300 ease-in-out z-50;
  }

  .mobile-menu.closed {
    transform: translateX(100%);
  }

  .mobile-menu.open {
    transform: translateX(0);
  }

  /* 移动端卡片 */
  .mobile-card {
    @apply p-4 rounded-lg;
  }

  /* 移动端按钮 */
  .mobile-button {
    @apply min-h-[48px] px-4 py-3 text-base;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .hover\:scale-105:hover {
    transform: none;
  }

  .touch-friendly {
    @apply min-h-[44px] min-w-[44px];
  }
}

/* 文化敏感的颜色系统 */
.cultural-chinese {
  --lucky-color: #dc2626; /* 红色 */
  --unlucky-color: #ffffff; /* 白色 */
}

.cultural-japanese {
  --lucky-color: #dc2626; /* 红色 */
  --unlucky-color: #000000; /* 黑色 */
}

.cultural-arabic {
  --lucky-color: #16a34a; /* 绿色 */
  --unlucky-color: #eab308; /* 黄色 */
}

.cultural-hindi {
  --lucky-color: #ea580c; /* 藏红花色 */
  --unlucky-color: #000000; /* 黑色 */
}

/* 响应式网格系统 */
.responsive-grid {
  @apply grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
}

.responsive-grid-blog {
  @apply grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
}

.responsive-grid-products {
  @apply grid gap-4 grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5;
}

/* 可访问性增强 */
.focus-visible {
  @apply outline-none ring-2 ring-mystical-500 ring-offset-2 ring-offset-background;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .mystical-card {
    @apply border-2 border-foreground;
  }

  .btn-mystical {
    @apply border-2 border-foreground;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .animate-mystical-glow,
  .animate-tarot-flip,
  .animate-crystal-shine,
  .animate-star-twinkle,
  .animate-float {
    animation: none;
  }

  .card-hover {
    @apply transition-none;
  }
}
