import { Redis } from 'ioredis';

// Redis客户端配置
let redis: Redis | null = null;

if (process.env.REDIS_URL) {
  redis = new Redis(process.env.REDIS_URL, {
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
    lazyConnect: true,
  });
}

// 缓存键前缀
const CACHE_PREFIX = process.env.REDIS_CACHE_PREFIX || 'mystical:';

// 缓存时间配置（秒）
export const CACHE_TIMES = {
  SHORT: 300, // 5分钟
  MEDIUM: 1800, // 30分钟
  LONG: 3600, // 1小时
  VERY_LONG: 86400, // 24小时
  WEEK: 604800, // 7天
} as const;

// 缓存键生成器
export function generateCacheKey(namespace: string, ...parts: (string | number)[]): string {
  return `${CACHE_PREFIX}${namespace}:${parts.join(':')}`;
}

// 通用缓存接口
export interface CacheAdapter {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  del(key: string): Promise<void>;
  exists(key: string): Promise<boolean>;
  clear(pattern?: string): Promise<void>;
}

// Redis缓存实现
class RedisCache implements CacheAdapter {
  private client: Redis;

  constructor(client: Redis) {
    this.client = client;
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.client.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Redis get error:', error);
      return null;
    }
  }

  async set<T>(key: string, value: T, ttl: number = CACHE_TIMES.MEDIUM): Promise<void> {
    try {
      await this.client.setex(key, ttl, JSON.stringify(value));
    } catch (error) {
      console.error('Redis set error:', error);
    }
  }

  async del(key: string): Promise<void> {
    try {
      await this.client.del(key);
    } catch (error) {
      console.error('Redis del error:', error);
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      console.error('Redis exists error:', error);
      return false;
    }
  }

  async clear(pattern?: string): Promise<void> {
    try {
      const keys = await this.client.keys(pattern || `${CACHE_PREFIX}*`);
      if (keys.length > 0) {
        await this.client.del(...keys);
      }
    } catch (error) {
      console.error('Redis clear error:', error);
    }
  }
}

// 内存缓存实现（备用）
class MemoryCache implements CacheAdapter {
  private cache = new Map<string, { value: any; expires: number }>();

  async get<T>(key: string): Promise<T | null> {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() > item.expires) {
      this.cache.delete(key);
      return null;
    }
    
    return item.value;
  }

  async set<T>(key: string, value: T, ttl: number = CACHE_TIMES.MEDIUM): Promise<void> {
    const expires = Date.now() + ttl * 1000;
    this.cache.set(key, { value, expires });
  }

  async del(key: string): Promise<void> {
    this.cache.delete(key);
  }

  async exists(key: string): Promise<boolean> {
    const item = this.cache.get(key);
    if (!item) return false;
    
    if (Date.now() > item.expires) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  async clear(pattern?: string): Promise<void> {
    if (pattern) {
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      for (const key of this.cache.keys()) {
        if (regex.test(key)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }
}

// 缓存实例
export const cache: CacheAdapter = redis ? new RedisCache(redis) : new MemoryCache();

// 缓存装饰器
export function cached<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  options: {
    keyGenerator: (...args: Parameters<T>) => string;
    ttl?: number;
    namespace?: string;
  }
): T {
  return (async (...args: Parameters<T>) => {
    const key = generateCacheKey(
      options.namespace || 'fn',
      options.keyGenerator(...args)
    );
    
    // 尝试从缓存获取
    const cached = await cache.get(key);
    if (cached !== null) {
      return cached;
    }
    
    // 执行函数并缓存结果
    const result = await fn(...args);
    await cache.set(key, result, options.ttl);
    
    return result;
  }) as T;
}

// 缓存管理器
export class CacheManager {
  // 文章缓存
  static async getArticle(slug: string, locale: string) {
    const key = generateCacheKey('article', slug, locale);
    return cache.get(key);
  }

  static async setArticle(slug: string, locale: string, article: any, ttl = CACHE_TIMES.LONG) {
    const key = generateCacheKey('article', slug, locale);
    return cache.set(key, article, ttl);
  }

  static async invalidateArticle(slug: string) {
    const pattern = generateCacheKey('article', slug, '*');
    return cache.clear(pattern);
  }

  // 商品缓存
  static async getProduct(slug: string, locale: string) {
    const key = generateCacheKey('product', slug, locale);
    return cache.get(key);
  }

  static async setProduct(slug: string, locale: string, product: any, ttl = CACHE_TIMES.LONG) {
    const key = generateCacheKey('product', slug, locale);
    return cache.set(key, product, ttl);
  }

  static async invalidateProduct(slug: string) {
    const pattern = generateCacheKey('product', slug, '*');
    return cache.clear(pattern);
  }

  // 测试缓存
  static async getTest(slug: string, locale: string) {
    const key = generateCacheKey('test', slug, locale);
    return cache.get(key);
  }

  static async setTest(slug: string, locale: string, test: any, ttl = CACHE_TIMES.VERY_LONG) {
    const key = generateCacheKey('test', slug, locale);
    return cache.set(key, test, ttl);
  }

  // 用户会话缓存
  static async getUserSession(sessionId: string) {
    const key = generateCacheKey('session', sessionId);
    return cache.get(key);
  }

  static async setUserSession(sessionId: string, session: any, ttl = CACHE_TIMES.LONG) {
    const key = generateCacheKey('session', sessionId);
    return cache.set(key, session, ttl);
  }

  // API响应缓存
  static async getApiResponse(endpoint: string, params: string) {
    const key = generateCacheKey('api', endpoint, params);
    return cache.get(key);
  }

  static async setApiResponse(endpoint: string, params: string, response: any, ttl = CACHE_TIMES.SHORT) {
    const key = generateCacheKey('api', endpoint, params);
    return cache.set(key, response, ttl);
  }

  // 清除所有缓存
  static async clearAll() {
    return cache.clear();
  }

  // 清除特定命名空间的缓存
  static async clearNamespace(namespace: string) {
    const pattern = generateCacheKey(namespace, '*');
    return cache.clear(pattern);
  }
}

// 缓存统计
export async function getCacheStats() {
  if (!redis) {
    return { type: 'memory', connected: true };
  }

  try {
    const info = await redis.info('memory');
    const keyspace = await redis.info('keyspace');
    
    return {
      type: 'redis',
      connected: true,
      memory: info,
      keyspace,
    };
  } catch (error) {
    return {
      type: 'redis',
      connected: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// 缓存健康检查
export async function cacheHealthCheck() {
  try {
    const testKey = generateCacheKey('health', 'check');
    const testValue = { timestamp: Date.now() };
    
    await cache.set(testKey, testValue, 60);
    const retrieved = await cache.get(testKey);
    await cache.del(testKey);
    
    return {
      status: 'healthy',
      latency: Date.now() - testValue.timestamp,
      canWrite: true,
      canRead: retrieved !== null,
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error',
      canWrite: false,
      canRead: false,
    };
  }
}

export default cache;
