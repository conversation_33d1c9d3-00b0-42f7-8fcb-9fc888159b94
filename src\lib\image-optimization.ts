import { ImageProps } from 'next/image';
import React from 'react';

// 图片尺寸配置
export const imageSizes = {
  thumbnail: { width: 150, height: 150 },
  small: { width: 300, height: 200 },
  medium: { width: 600, height: 400 },
  large: { width: 1200, height: 800 },
  hero: { width: 1920, height: 1080 },
  card: { width: 400, height: 300 },
  avatar: { width: 100, height: 100 },
  logo: { width: 200, height: 200 },
} as const;

// 响应式图片尺寸
export const responsiveSizes = {
  mobile: '(max-width: 768px) 100vw',
  tablet: '(max-width: 1024px) 50vw',
  desktop: '33vw',
  full: '100vw',
  half: '50vw',
  third: '33vw',
  quarter: '25vw',
} as const;

// 图片质量配置
export const imageQuality = {
  low: 50,
  medium: 75,
  high: 90,
  lossless: 100,
} as const;

// 图片格式优先级
export const imageFormats = ['image/avif', 'image/webp', 'image/jpeg'] as const;

// 生成优化的图片属性
export function getOptimizedImageProps(
  src: string,
  alt: string,
  options: {
    size?: keyof typeof imageSizes;
    quality?: keyof typeof imageQuality;
    priority?: boolean;
    fill?: boolean;
    sizes?: string;
    className?: string;
  } = {}
): Partial<ImageProps> {
  const {
    size = 'medium',
    quality = 'high',
    priority = false,
    fill = false,
    sizes,
    className,
  } = options;

  const dimensions = imageSizes[size];
  
  const baseProps: Partial<ImageProps> = {
    src,
    alt,
    quality: imageQuality[quality],
    priority,
    className,
  };

  if (fill) {
    return {
      ...baseProps,
      fill: true,
      sizes: sizes || responsiveSizes.full,
      style: { objectFit: 'cover' },
    };
  }

  return {
    ...baseProps,
    width: dimensions.width,
    height: dimensions.height,
    sizes: sizes || `(max-width: 768px) 100vw, ${dimensions.width}px`,
  };
}

// 生成响应式图片sizes属性
export function generateSizes(breakpoints: {
  mobile?: string;
  tablet?: string;
  desktop?: string;
  default?: string;
}): string {
  const sizes: string[] = [];
  
  if (breakpoints.mobile) {
    sizes.push(`(max-width: 768px) ${breakpoints.mobile}`);
  }
  
  if (breakpoints.tablet) {
    sizes.push(`(max-width: 1024px) ${breakpoints.tablet}`);
  }
  
  if (breakpoints.desktop) {
    sizes.push(`(max-width: 1440px) ${breakpoints.desktop}`);
  }
  
  sizes.push(breakpoints.default || '100vw');
  
  return sizes.join(', ');
}

// 图片URL优化（适用于外部图片服务）
export function optimizeImageUrl(
  url: string,
  options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'auto' | 'webp' | 'avif' | 'jpg' | 'png';
    fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
  } = {}
): string {
  // 如果是Cloudinary URL，添加变换参数
  if (url.includes('cloudinary.com')) {
    return optimizeCloudinaryUrl(url, options);
  }
  
  // 如果是其他CDN，可以添加相应的优化逻辑
  return url;
}

// Cloudinary URL优化
function optimizeCloudinaryUrl(
  url: string,
  options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'auto' | 'webp' | 'avif' | 'jpg' | 'png';
    fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
  }
): string {
  const { width, height, quality = 80, format = 'auto', fit = 'cover' } = options;
  
  // 构建变换参数
  const transformations: string[] = [];
  
  if (width) transformations.push(`w_${width}`);
  if (height) transformations.push(`h_${height}`);
  if (quality) transformations.push(`q_${quality}`);
  if (format) transformations.push(`f_${format}`);
  if (fit) transformations.push(`c_${fit}`);
  
  // 添加自动优化
  transformations.push('fl_progressive', 'fl_immutable_cache');
  
  const transformString = transformations.join(',');
  
  // 插入变换参数到URL中
  return url.replace('/upload/', `/upload/${transformString}/`);
}

// 预加载关键图片
export function preloadImage(src: string, as: 'image' = 'image'): void {
  if (typeof window !== 'undefined') {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = as;
    link.href = src;
    document.head.appendChild(link);
  }
}

// 懒加载图片观察器
export function createImageObserver(
  callback: (entry: IntersectionObserverEntry) => void,
  options: IntersectionObserverInit = {}
): IntersectionObserver | null {
  if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
    return null;
  }

  const defaultOptions: IntersectionObserverInit = {
    root: null,
    rootMargin: '50px',
    threshold: 0.1,
    ...options,
  };

  return new IntersectionObserver((entries) => {
    entries.forEach(callback);
  }, defaultOptions);
}

// 图片加载状态管理
export function useImageLoadingState() {
  const [loadingStates, setLoadingStates] = React.useState<Record<string, boolean>>({});

  const setLoading = React.useCallback((src: string, loading: boolean) => {
    setLoadingStates(prev => ({
      ...prev,
      [src]: loading,
    }));
  }, []);

  const isLoading = React.useCallback((src: string) => {
    return loadingStates[src] ?? true;
  }, [loadingStates]);

  return { setLoading, isLoading };
}

// 图片错误处理
export function getImageFallback(type: 'avatar' | 'product' | 'article' | 'general' = 'general'): string {
  const fallbacks = {
    avatar: '/images/fallback/avatar.png',
    product: '/images/fallback/product.png',
    article: '/images/fallback/article.png',
    general: '/images/fallback/placeholder.png',
  };
  
  return fallbacks[type];
}

// 生成图片的srcSet
export function generateSrcSet(
  baseSrc: string,
  sizes: number[] = [320, 640, 768, 1024, 1280, 1920]
): string {
  return sizes
    .map(size => {
      const optimizedSrc = optimizeImageUrl(baseSrc, { width: size });
      return `${optimizedSrc} ${size}w`;
    })
    .join(', ');
}

// 计算图片的宽高比
export function calculateAspectRatio(width: number, height: number): string {
  const gcd = (a: number, b: number): number => (b === 0 ? a : gcd(b, a % b));
  const divisor = gcd(width, height);
  return `${width / divisor}/${height / divisor}`;
}

// 图片性能监控
export function trackImagePerformance(src: string, startTime: number): void {
  if (typeof window !== 'undefined' && 'performance' in window) {
    const loadTime = performance.now() - startTime;
    
    // 发送性能数据到分析服务
    if (loadTime > 2000) {
      console.warn(`Slow image loading detected: ${src} took ${loadTime}ms`);
    }
    
    // 可以集成到分析服务中
    // analytics.track('image_load_time', { src, loadTime });
  }
}
