'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import type { Locale } from '@/types';
import { PageLayout } from '@/components/layout/page-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface DailyTarotPageProps {
  params: { locale: Locale };
}

// 塔罗牌数据
const tarotCards = [
  { id: 1, name: 'The Fool', emoji: '🃏', meaning: 'New beginnings, innocence, spontaneity' },
  { id: 2, name: 'The Magician', emoji: '🎩', meaning: 'Manifestation, resourcefulness, power' },
  { id: 3, name: 'The High Priestess', emoji: '🌙', meaning: 'Intuition, sacred knowledge, divine feminine' },
  { id: 4, name: 'The Empress', emoji: '👑', meaning: 'Femininity, beauty, nature, abundance' },
  { id: 5, name: 'The Emperor', emoji: '⚔️', meaning: 'Authority, establishment, structure, father figure' },
  { id: 6, name: 'The Hierophant', emoji: '🏛️', meaning: 'Spiritual wisdom, religious beliefs, conformity' },
  { id: 7, name: 'The Lovers', emoji: '💕', meaning: 'Love, harmony, relationships, values alignment' },
  { id: 8, name: 'The Chariot', emoji: '🏇', meaning: 'Control, willpower, success, determination' },
  { id: 9, name: 'Strength', emoji: '🦁', meaning: 'Strength, courage, persuasion, influence' },
  { id: 10, name: 'The Hermit', emoji: '🕯️', meaning: 'Soul searching, introspection, inner guidance' },
  { id: 11, name: 'Wheel of Fortune', emoji: '🎡', meaning: 'Good luck, karma, life cycles, destiny' },
  { id: 12, name: 'Justice', emoji: '⚖️', meaning: 'Justice, fairness, truth, cause and effect' },
  { id: 13, name: 'The Hanged Man', emoji: '🙃', meaning: 'Suspension, restriction, letting go' },
  { id: 14, name: 'Death', emoji: '💀', meaning: 'Endings, beginnings, change, transformation' },
  { id: 15, name: 'Temperance', emoji: '🍷', meaning: 'Balance, moderation, patience, purpose' },
  { id: 16, name: 'The Devil', emoji: '😈', meaning: 'Bondage, addiction, sexuality, materialism' },
  { id: 17, name: 'The Tower', emoji: '🏗️', meaning: 'Sudden change, upheaval, chaos, revelation' },
  { id: 18, name: 'The Star', emoji: '⭐', meaning: 'Hope, faith, purpose, renewal, spirituality' },
  { id: 19, name: 'The Moon', emoji: '🌙', meaning: 'Illusion, fear, anxiety, subconscious, intuition' },
  { id: 20, name: 'The Sun', emoji: '☀️', meaning: 'Positivity, fun, warmth, success, vitality' },
  { id: 21, name: 'Judgement', emoji: '📯', meaning: 'Judgement, rebirth, inner calling, absolution' },
  { id: 22, name: 'The World', emoji: '🌍', meaning: 'Completion, accomplishment, travel, fulfillment' },
];

export default function DailyTarotPage({ params: { locale } }: DailyTarotPageProps) {
  const t = useTranslations();
  const [selectedCard, setSelectedCard] = useState<typeof tarotCards[0] | null>(null);
  const [isReading, setIsReading] = useState(false);
  const [reading, setReading] = useState<string>('');
  const [question, setQuestion] = useState('');
  const [hasDrawn, setHasDrawn] = useState(false);

  const drawCard = async () => {
    if (!question.trim()) {
      alert('Please enter a question first');
      return;
    }

    setIsReading(true);
    setHasDrawn(true);
    
    // 随机选择一张牌
    const randomCard = tarotCards[Math.floor(Math.random() * tarotCards.length)];
    setSelectedCard(randomCard);
    
    try {
      // 调用AI生成解读
      const response = await fetch('/api/ai/tarot-reading', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          card: randomCard.name,
          question: question,
          locale: locale,
          type: 'daily',
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setReading(data.reading);
      } else {
        // 备用解读
        setReading(generateFallbackReading(randomCard, question, locale));
      }
    } catch (error) {
      console.error('Failed to get AI reading:', error);
      setReading(generateFallbackReading(randomCard, question, locale));
    } finally {
      setIsReading(false);
    }
  };

  const generateFallbackReading = (card: typeof tarotCards[0], question: string, locale: Locale) => {
    const readings = {
      en: `The ${card.name} appears in response to your question about "${question}". This card represents ${card.meaning.toLowerCase()}. Today, the universe encourages you to embrace the energy of this card and apply its wisdom to your current situation. Trust in the guidance that comes from within and remain open to the messages the universe is sending you.`,
      zh: `${card.name}出现来回应你关于"${question}"的问题。这张牌代表${card.meaning}。今天，宇宙鼓励你拥抱这张牌的能量，并将其智慧应用到你当前的情况中。相信内心的指引，保持对宇宙传递给你的信息的开放态度。`,
      es: `El ${card.name} aparece en respuesta a tu pregunta sobre "${question}". Esta carta representa ${card.meaning}. Hoy, el universo te anima a abrazar la energía de esta carta y aplicar su sabiduría a tu situación actual.`,
      pt: `O ${card.name} aparece em resposta à sua pergunta sobre "${question}". Esta carta representa ${card.meaning}. Hoje, o universo o encoraja a abraçar a energia desta carta e aplicar sua sabedoria à sua situação atual.`,
      hi: `"${question}" के बारे में आपके प्रश्न के उत्तर में ${card.name} प्रकट होता है। यह कार्ड ${card.meaning} का प्रतिनिधित्व करता है। आज, ब्रह्मांड आपको इस कार्ड की ऊर्जा को अपनाने और अपनी वर्तमान स्थिति में इसकी बुद्धि को लागू करने के लिए प्रोत्साहित करता है।`,
      ja: `"${question}"についてのあなたの質問に応えて${card.name}が現れました。このカードは${card.meaning}を表しています。今日、宇宙はあなたにこのカードのエネルギーを受け入れ、その知恵を現在の状況に適用することを奨励しています。`,
    };
    
    return readings[locale] || readings.en;
  };

  const resetReading = () => {
    setSelectedCard(null);
    setReading('');
    setQuestion('');
    setHasDrawn(false);
    setIsReading(false);
  };

  return (
    <PageLayout locale={locale}>
      <div className="min-h-screen bg-gradient-to-br from-mystical-50 via-cosmic-50 to-mystical-100 dark:from-mystical-950 dark:via-cosmic-950 dark:to-mystical-900">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto">
            {/* Header */}
            <div className="text-center mb-12">
              <div className="text-6xl mb-6">🔮</div>
              <h1 className="text-responsive-xl font-serif font-bold text-gradient mb-6">
                {t('tarot.daily')}
              </h1>
              <p className="text-responsive-md text-muted-foreground max-w-2xl mx-auto">
                Draw a card to receive guidance and insights for your day ahead
              </p>
            </div>

            {!hasDrawn ? (
              /* Question Input */
              <Card variant="mystical" className="max-w-2xl mx-auto mb-8">
                <CardHeader>
                  <CardTitle className="text-center">Ask Your Question</CardTitle>
                  <CardDescription className="text-center">
                    Focus on what you'd like guidance about today
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <textarea
                      value={question}
                      onChange={(e) => setQuestion(e.target.value)}
                      placeholder="What would you like guidance about today?"
                      className="w-full p-4 rounded-lg border border-border bg-background resize-none h-24"
                      maxLength={200}
                    />
                    <div className="text-center">
                      <Button 
                        variant="mystical" 
                        size="lg" 
                        onClick={drawCard}
                        disabled={!question.trim() || isReading}
                      >
                        {isReading ? 'Drawing Card...' : 'Draw Your Card'}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              /* Reading Results */
              <div className="space-y-8">
                {/* Selected Card */}
                {selectedCard && (
                  <Card variant="cosmic" className="max-w-md mx-auto text-center">
                    <CardHeader>
                      <div className="text-6xl mb-4">{selectedCard.emoji}</div>
                      <CardTitle className="text-2xl">{selectedCard.name}</CardTitle>
                      <CardDescription>{selectedCard.meaning}</CardDescription>
                    </CardHeader>
                  </Card>
                )}

                {/* Question */}
                <Card variant="glass" className="max-w-2xl mx-auto">
                  <CardHeader>
                    <CardTitle className="text-center">Your Question</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-center italic">"{question}"</p>
                  </CardContent>
                </Card>

                {/* Reading */}
                <Card variant="mystical" className="max-w-3xl mx-auto">
                  <CardHeader>
                    <CardTitle className="text-center">Your Reading</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {isReading ? (
                      <div className="text-center py-8">
                        <div className="animate-spin text-4xl mb-4">🔮</div>
                        <p>The cards are speaking...</p>
                      </div>
                    ) : (
                      <div className="prose prose-lg max-w-none">
                        <p className="text-lg leading-relaxed">{reading}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Actions */}
                <div className="text-center space-x-4">
                  <Button variant="outline" onClick={resetReading}>
                    Draw Another Card
                  </Button>
                  <Button variant="mystical">
                    Save Reading
                  </Button>
                </div>
              </div>
            )}

            {/* Information */}
            <Card variant="glass" className="max-w-2xl mx-auto mt-12">
              <CardHeader>
                <CardTitle className="text-center">About Daily Tarot</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center space-y-4">
                  <p>
                    Daily tarot readings provide guidance and insight for your day ahead. 
                    Each card carries unique energy and wisdom to help you navigate life's journey.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                    <div className="text-center">
                      <div className="text-2xl mb-2">🎯</div>
                      <h4 className="font-semibold">Focus</h4>
                      <p className="text-sm text-muted-foreground">
                        Concentrate on your question
                      </p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl mb-2">🔮</div>
                      <h4 className="font-semibold">Trust</h4>
                      <p className="text-sm text-muted-foreground">
                        Trust in the card's message
                      </p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl mb-2">✨</div>
                      <h4 className="font-semibold">Apply</h4>
                      <p className="text-sm text-muted-foreground">
                        Apply the wisdom to your day
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
