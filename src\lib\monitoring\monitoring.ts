// 监控和分析服务集成

// Umami 分析
export function initUmami() {
  if (typeof window === 'undefined') return;
  
  const websiteId = process.env.NEXT_PUBLIC_UMAMI_WEBSITE_ID;
  const umamiUrl = process.env.NEXT_PUBLIC_UMAMI_URL;
  
  if (!websiteId || !umamiUrl) {
    console.warn('Umami configuration missing');
    return;
  }

  // 创建 Umami 脚本
  const script = document.createElement('script');
  script.async = true;
  script.defer = true;
  script.src = `${umamiUrl}/script.js`;
  script.setAttribute('data-website-id', websiteId);
  script.setAttribute('data-domains', window.location.hostname);
  
  document.head.appendChild(script);
}

// Google Analytics 4
export function initGA4() {
  if (typeof window === 'undefined') return;
  
  const measurementId = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID;
  if (!measurementId) {
    console.warn('Google Analytics configuration missing');
    return;
  }

  // 加载 gtag
  const script1 = document.createElement('script');
  script1.async = true;
  script1.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId}`;
  document.head.appendChild(script1);

  // 初始化 gtag
  const script2 = document.createElement('script');
  script2.innerHTML = `
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', '${measurementId}', {
      page_title: document.title,
      page_location: window.location.href,
    });
  `;
  document.head.appendChild(script2);

  // 设置全局 gtag 函数
  (window as any).gtag = function() {
    (window as any).dataLayer.push(arguments);
  };
}

// 自定义事件跟踪
export function trackEvent(eventName: string, parameters: Record<string, any> = {}) {
  // Umami 事件跟踪
  if ((window as any).umami) {
    (window as any).umami.track(eventName, parameters);
  }

  // Google Analytics 事件跟踪
  if ((window as any).gtag) {
    (window as any).gtag('event', eventName, parameters);
  }

  // 自定义分析
  trackCustomEvent(eventName, parameters);
}

// 自定义分析事件
function trackCustomEvent(eventName: string, parameters: Record<string, any>) {
  const eventData = {
    event: eventName,
    timestamp: new Date().toISOString(),
    url: window.location.href,
    userAgent: navigator.userAgent,
    ...parameters,
  };

  // 发送到自定义分析端点
  fetch('/api/analytics/events', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(eventData),
  }).catch(error => {
    console.warn('Failed to track custom event:', error);
  });
}

// 页面浏览跟踪
export function trackPageView(url: string, title?: string) {
  trackEvent('page_view', {
    page_title: title || document.title,
    page_location: url,
  });
}

// 用户行为跟踪
export function trackUserAction(action: string, category: string, label?: string, value?: number) {
  trackEvent('user_action', {
    event_category: category,
    event_label: label,
    value: value,
    action: action,
  });
}

// 电商事件跟踪
export function trackPurchase(transactionId: string, value: number, currency: string, items: any[]) {
  trackEvent('purchase', {
    transaction_id: transactionId,
    value: value,
    currency: currency,
    items: items,
  });
}

export function trackAddToCart(itemId: string, itemName: string, category: string, value: number) {
  trackEvent('add_to_cart', {
    currency: 'USD',
    value: value,
    items: [{
      item_id: itemId,
      item_name: itemName,
      item_category: category,
      quantity: 1,
      price: value,
    }],
  });
}

// AI服务使用跟踪
export function trackAIUsage(service: string, type: string, tokens?: number, cost?: number) {
  trackEvent('ai_usage', {
    ai_service: service,
    usage_type: type,
    tokens_used: tokens,
    cost: cost,
  });
}

// 测试完成跟踪
export function trackTestCompletion(testId: string, testName: string, result: string, duration: number) {
  trackEvent('test_completion', {
    test_id: testId,
    test_name: testName,
    test_result: result,
    completion_time: duration,
  });
}

// 错误跟踪
export function trackError(error: Error, context?: Record<string, any>) {
  trackEvent('error', {
    error_message: error.message,
    error_stack: error.stack,
    error_name: error.name,
    ...context,
  });

  // 同时发送到 Sentry（如果配置了）
  if ((window as any).Sentry) {
    (window as any).Sentry.captureException(error, {
      extra: context,
    });
  }
}

// 性能监控
export function trackPerformance(metric: string, value: number, unit: string = 'ms') {
  trackEvent('performance', {
    metric_name: metric,
    metric_value: value,
    metric_unit: unit,
  });
}

// 搜索跟踪
export function trackSearch(query: string, category?: string, resultsCount?: number) {
  trackEvent('search', {
    search_term: query,
    search_category: category,
    results_count: resultsCount,
  });
}

// 社交分享跟踪
export function trackShare(platform: string, url: string, title: string) {
  trackEvent('share', {
    platform: platform,
    shared_url: url,
    shared_title: title,
  });
}

// 初始化所有监控服务
export function initMonitoring() {
  if (typeof window === 'undefined') return;

  // 初始化分析服务
  initUmami();
  initGA4();

  // 初始化 Sentry（如果配置了）
  const sentryDsn = process.env.NEXT_PUBLIC_SENTRY_DSN;
  if (sentryDsn && (window as any).Sentry) {
    (window as any).Sentry.init({
      dsn: sentryDsn,
      environment: process.env.NODE_ENV,
      tracesSampleRate: 0.1,
    });
  }

  // 监听未处理的错误
  window.addEventListener('error', (event) => {
    trackError(new Error(event.message), {
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
    });
  });

  // 监听未处理的 Promise 拒绝
  window.addEventListener('unhandledrejection', (event) => {
    trackError(new Error(event.reason), {
      type: 'unhandled_promise_rejection',
    });
  });

  console.log('🔍 监控服务已初始化');
}

// 监控数据导出
export async function exportAnalyticsData(startDate: string, endDate: string) {
  try {
    const response = await fetch('/api/analytics/export', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        start_date: startDate,
        end_date: endDate,
      }),
    });

    if (response.ok) {
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analytics-${startDate}-${endDate}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    }
  } catch (error) {
    console.error('Failed to export analytics data:', error);
    trackError(error as Error, { action: 'export_analytics' });
  }
}

// 实时监控仪表板数据
export async function getDashboardData() {
  try {
    const response = await fetch('/api/analytics/dashboard');
    if (response.ok) {
      return await response.json();
    }
  } catch (error) {
    console.error('Failed to fetch dashboard data:', error);
    return null;
  }
}

export default {
  init: initMonitoring,
  trackEvent,
  trackPageView,
  trackUserAction,
  trackPurchase,
  trackAddToCart,
  trackAIUsage,
  trackTestCompletion,
  trackError,
  trackPerformance,
  trackSearch,
  trackShare,
  exportAnalyticsData,
  getDashboardData,
};
