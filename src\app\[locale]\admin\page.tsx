'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import type { Locale } from '@/types';
import { PageLayout } from '@/components/layout/page-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface AdminPageProps {
  params: { locale: Locale };
}

interface Stats {
  articles: number;
  products: number;
  users: number;
  tests: number;
  orders: number;
}

export default function AdminPage({ params: { locale } }: AdminPageProps) {
  const t = useTranslations();
  const [stats, setStats] = useState<Stats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/health');
      if (response.ok) {
        const data = await response.json();
        setStats(data.stats || {
          articles: 0,
          products: 0,
          users: 0,
          tests: 0,
          orders: 0,
        });
      }
    } catch (error) {
      console.error('Failed to fetch stats:', error);
      setStats({
        articles: 0,
        products: 0,
        users: 0,
        tests: 0,
        orders: 0,
      });
    } finally {
      setLoading(false);
    }
  };

  const adminSections = [
    {
      title: 'Content Management',
      description: 'Manage articles, products, and other content',
      icon: '📝',
      items: [
        { name: 'Articles', count: stats?.articles || 0, href: '/admin/articles' },
        { name: 'Products', count: stats?.products || 0, href: '/admin/products' },
        { name: 'Categories', count: 0, href: '/admin/categories' },
        { name: 'Tags', count: 0, href: '/admin/tags' },
      ],
    },
    {
      title: 'User Management',
      description: 'Manage users, subscriptions, and permissions',
      icon: '👥',
      items: [
        { name: 'Users', count: stats?.users || 0, href: '/admin/users' },
        { name: 'Subscriptions', count: 0, href: '/admin/subscriptions' },
        { name: 'Permissions', count: 0, href: '/admin/permissions' },
      ],
    },
    {
      title: 'Mystical Services',
      description: 'Manage tests, AI services, and mystical content',
      icon: '🔮',
      items: [
        { name: 'Tests', count: stats?.tests || 0, href: '/admin/tests' },
        { name: 'AI Requests', count: 0, href: '/admin/ai-requests' },
        { name: 'Test Sessions', count: 0, href: '/admin/test-sessions' },
      ],
    },
    {
      title: 'E-commerce',
      description: 'Manage orders, payments, and inventory',
      icon: '🛍️',
      items: [
        { name: 'Orders', count: stats?.orders || 0, href: '/admin/orders' },
        { name: 'Payments', count: 0, href: '/admin/payments' },
        { name: 'Inventory', count: 0, href: '/admin/inventory' },
      ],
    },
    {
      title: 'Analytics',
      description: 'View site analytics and performance metrics',
      icon: '📊',
      items: [
        { name: 'Site Analytics', count: 0, href: '/admin/analytics' },
        { name: 'Performance', count: 0, href: '/admin/performance' },
        { name: 'SEO Reports', count: 0, href: '/admin/seo' },
      ],
    },
    {
      title: 'System',
      description: 'System settings, cache, and maintenance',
      icon: '⚙️',
      items: [
        { name: 'Settings', count: 0, href: '/admin/settings' },
        { name: 'Cache Management', count: 0, href: '/admin/cache' },
        { name: 'System Health', count: 0, href: '/admin/health' },
      ],
    },
  ];

  const getHref = (href: string) => {
    return locale === 'en' ? href : `/${locale}${href}`;
  };

  if (loading) {
    return (
      <PageLayout locale={locale}>
        <div className="min-h-screen bg-gradient-to-br from-background via-muted/20 to-background">
          <div className="container mx-auto px-4 py-16">
            <div className="text-center">
              <div className="animate-spin text-4xl mb-4">⚙️</div>
              <p>Loading admin dashboard...</p>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout locale={locale}>
      <div className="min-h-screen bg-gradient-to-br from-background via-muted/20 to-background">
        <div className="container mx-auto px-4 py-16">
          {/* Header */}
          <div className="mb-12">
            <h1 className="text-responsive-xl font-serif font-bold text-gradient mb-4">
              Admin Dashboard
            </h1>
            <p className="text-responsive-md text-muted-foreground">
              Manage your mystical website content and settings
            </p>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-12">
            <Card variant="mystical">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Articles</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.articles || 0}</div>
              </CardContent>
            </Card>
            
            <Card variant="mystical">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Products</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.products || 0}</div>
              </CardContent>
            </Card>
            
            <Card variant="mystical">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Users</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.users || 0}</div>
              </CardContent>
            </Card>
            
            <Card variant="mystical">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Tests</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.tests || 0}</div>
              </CardContent>
            </Card>
            
            <Card variant="mystical">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Orders</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.orders || 0}</div>
              </CardContent>
            </Card>
          </div>

          {/* Admin Sections */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {adminSections.map((section) => (
              <Card key={section.title} variant="cosmic" hover>
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <span className="text-3xl">{section.icon}</span>
                    <div>
                      <CardTitle className="text-lg">{section.title}</CardTitle>
                      <CardDescription>{section.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {section.items.map((item) => (
                      <div key={item.name} className="flex items-center justify-between">
                        <a
                          href={getHref(item.href)}
                          className="text-sm hover:text-primary transition-colors"
                        >
                          {item.name}
                        </a>
                        <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                          {item.count}
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Quick Actions */}
          <div className="mt-12">
            <h2 className="text-2xl font-bold mb-6">Quick Actions</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button variant="mystical" className="h-auto py-4 flex-col">
                <span className="text-2xl mb-2">📝</span>
                <span>New Article</span>
              </Button>
              
              <Button variant="cosmic" className="h-auto py-4 flex-col">
                <span className="text-2xl mb-2">🛍️</span>
                <span>Add Product</span>
              </Button>
              
              <Button variant="outline" className="h-auto py-4 flex-col">
                <span className="text-2xl mb-2">🧪</span>
                <span>Create Test</span>
              </Button>
              
              <Button variant="outline" className="h-auto py-4 flex-col">
                <span className="text-2xl mb-2">📊</span>
                <span>View Analytics</span>
              </Button>
            </div>
          </div>

          {/* System Status */}
          <div className="mt-12">
            <Card variant="glass">
              <CardHeader>
                <CardTitle>System Status</CardTitle>
                <CardDescription>Current system health and performance</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-green-500 text-2xl mb-1">✅</div>
                    <div className="text-sm font-medium">Database</div>
                    <div className="text-xs text-muted-foreground">Healthy</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-green-500 text-2xl mb-1">✅</div>
                    <div className="text-sm font-medium">Cache</div>
                    <div className="text-xs text-muted-foreground">Active</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-green-500 text-2xl mb-1">✅</div>
                    <div className="text-sm font-medium">AI Services</div>
                    <div className="text-xs text-muted-foreground">Available</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-green-500 text-2xl mb-1">✅</div>
                    <div className="text-sm font-medium">CDN</div>
                    <div className="text-xs text-muted-foreground">Optimized</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
