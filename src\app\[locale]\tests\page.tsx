import { useTranslations } from 'next-intl';
import { getTranslations } from 'next-intl/server';
import type { Metadata } from 'next';
import type { Locale } from '@/types';
import { PageLayout } from '@/components/layout/page-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { generateSEOMetadata } from '@/components/seo/seo-head';
import Link from 'next/link';

interface TestsPageProps {
  params: { locale: Locale };
}

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: Locale };
}): Promise<Metadata> {
  const t = await getTranslations({ locale, namespace: 'seo' });

  const seoData = {
    title: locale === 'zh' ? '神秘测试中心 - 神秘洞察' :
           locale === 'es' ? 'Centro de Pruebas Místicas - Perspectivas Místicas' :
           locale === 'pt' ? 'Centro de Testes Místicos - Percepções Místicas' :
           locale === 'hi' ? 'रहस्यमय परीक्षण केंद्र - रहस्यमय अंतर्दृष्टि' :
           locale === 'ja' ? 'ミスティカルテストセンター - ミスティカル・インサイト' :
           'Mystical Test Center - Mystical Insights',
    description: locale === 'zh' ? '通过互动测试发现你的灵性特质、性格类型和神秘天赋' :
                locale === 'es' ? 'Descubre tus rasgos espirituales, tipo de personalidad y dones místicos a través de pruebas interactivas' :
                locale === 'pt' ? 'Descubra seus traços espirituais, tipo de personalidade e dons místicos através de testes interativos' :
                locale === 'hi' ? 'इंटरैक्टिव परीक्षणों के माध्यम से अपने आध्यात्मिक लक्षण, व्यक्तित्व प्रकार और रहस्यमय उपहारों की खोज करें' :
                locale === 'ja' ? 'インタラクティブなテストを通してあなたのスピリチュアルな特性、性格タイプ、神秘的な才能を発見' :
                'Discover your spiritual traits, personality type, and mystical gifts through interactive tests',
    keywords: ['mystical tests', 'personality quiz', 'spiritual assessment', 'psychic abilities', 'aura reading'],
    ogType: 'website' as const,
    twitterCard: 'summary_large_image' as const,
  };

  return generateSEOMetadata({
    seo: seoData,
    locale,
    path: '/tests',
  });
}

// 测试数据
const mysticalTests = [
  {
    id: 'aura-color',
    icon: '🌈',
    title: {
      en: 'What\'s Your Aura Color?',
      zh: '你的气场颜色是什么？',
      es: '¿Cuál es el Color de tu Aura?',
      pt: 'Qual é a Cor da sua Aura?',
      hi: 'आपकी आभा का रंग क्या है?',
      ja: 'あなたのオーラの色は何ですか？',
    },
    description: {
      en: 'Discover the color of your spiritual energy field and what it reveals about your personality',
      zh: '发现你的灵性能量场颜色，以及它揭示的你的性格特质',
      es: 'Descubre el color de tu campo de energía espiritual y lo que revela sobre tu personalidad',
      pt: 'Descubra a cor do seu campo de energia espiritual e o que revela sobre sua personalidade',
      hi: 'अपने आध्यात्मिक ऊर्जा क्षेत्र का रंग खोजें और यह आपके व्यक्तित्व के बारे में क्या प्रकट करता है',
      ja: 'あなたのスピリチュアルエネルギーフィールドの色と、それがあなたの性格について何を明かすかを発見',
    },
    duration: '5 min',
    questions: 12,
    difficulty: 'Easy',
    category: 'Spiritual',
    featured: true,
  },
  {
    id: 'psychic-abilities',
    icon: '🔮',
    title: {
      en: 'Psychic Abilities Assessment',
      zh: '超能力评估',
      es: 'Evaluación de Habilidades Psíquicas',
      pt: 'Avaliação de Habilidades Psíquicas',
      hi: 'मानसिक क्षमताओं का मूल्यांकन',
      ja: 'サイキック能力アセスメント',
    },
    description: {
      en: 'Explore your natural psychic gifts and intuitive abilities',
      zh: '探索你天生的超能力和直觉能力',
      es: 'Explora tus dones psíquicos naturales y habilidades intuitivas',
      pt: 'Explore seus dons psíquicos naturais e habilidades intuitivas',
      hi: 'अपने प्राकृतिक मानसिक उपहारों और सहज क्षमताओं का अन्वेषण करें',
      ja: 'あなたの生来のサイキックギフトと直感的能力を探求',
    },
    duration: '10 min',
    questions: 20,
    difficulty: 'Medium',
    category: 'Psychic',
    featured: true,
  },
  {
    id: 'spirit-animal',
    icon: '🦅',
    title: {
      en: 'Find Your Spirit Animal',
      zh: '找到你的守护动物',
      es: 'Encuentra tu Animal Espiritual',
      pt: 'Encontre seu Animal Espiritual',
      hi: 'अपना आत्मा पशु खोजें',
      ja: 'あなたのスピリットアニマルを見つける',
    },
    description: {
      en: 'Discover which animal spirit guides and protects you on your life journey',
      zh: '发现哪种动物精神在你的人生旅程中指导和保护你',
      es: 'Descubre qué espíritu animal te guía y protege en tu viaje de vida',
      pt: 'Descubra qual espírito animal te guia e protege em sua jornada de vida',
      hi: 'पता करें कि कौन सा पशु आत्मा आपके जीवन यात्रा में आपका मार्गदर्शन और सुरक्षा करता है',
      ja: 'あなたの人生の旅であなたを導き守る動物の霊を発見',
    },
    duration: '7 min',
    questions: 15,
    difficulty: 'Easy',
    category: 'Spiritual',
    featured: false,
  },
  {
    id: 'chakra-balance',
    icon: '🧘',
    title: {
      en: 'Chakra Balance Test',
      zh: '脉轮平衡测试',
      es: 'Prueba de Equilibrio de Chakras',
      pt: 'Teste de Equilíbrio dos Chakras',
      hi: 'चक्र संतुलन परीक्षण',
      ja: 'チャクラバランステスト',
    },
    description: {
      en: 'Assess the balance of your seven main chakras and energy centers',
      zh: '评估你七个主要脉轮和能量中心的平衡状态',
      es: 'Evalúa el equilibrio de tus siete chakras principales y centros de energía',
      pt: 'Avalie o equilíbrio dos seus sete chakras principais e centros de energia',
      hi: 'अपने सात मुख्य चक्रों और ऊर्जा केंद्रों के संतुलन का आकलन करें',
      ja: 'あなたの7つの主要なチャクラとエネルギーセンターのバランスを評価',
    },
    duration: '8 min',
    questions: 21,
    difficulty: 'Medium',
    category: 'Energy',
    featured: true,
  },
  {
    id: 'past-life',
    icon: '⏳',
    title: {
      en: 'Past Life Memories',
      zh: '前世记忆',
      es: 'Memorias de Vidas Pasadas',
      pt: 'Memórias de Vidas Passadas',
      hi: 'पूर्व जन्म की यादें',
      ja: '前世の記憶',
    },
    description: {
      en: 'Explore potential memories and connections from your previous incarnations',
      zh: '探索你前世转世的潜在记忆和联系',
      es: 'Explora memorias potenciales y conexiones de tus encarnaciones anteriores',
      pt: 'Explore memórias potenciais e conexões de suas encarnações anteriores',
      hi: 'अपने पिछले अवतारों से संभावित यादों और संबंधों का अन्वेषण करें',
      ja: 'あなたの前世からの潜在的な記憶とつながりを探求',
    },
    duration: '12 min',
    questions: 18,
    difficulty: 'Hard',
    category: 'Spiritual',
    featured: false,
  },
  {
    id: 'crystal-match',
    icon: '💎',
    title: {
      en: 'Your Perfect Crystal Match',
      zh: '你的完美水晶匹配',
      es: 'Tu Cristal Perfecto',
      pt: 'Seu Cristal Perfeito',
      hi: 'आपका परफेक्ट क्रिस्टल मैच',
      ja: 'あなたにぴったりのクリスタル',
    },
    description: {
      en: 'Find the crystal that resonates most with your energy and spiritual needs',
      zh: '找到与你的能量和灵性需求最共鸣的水晶',
      es: 'Encuentra el cristal que más resuena con tu energía y necesidades espirituales',
      pt: 'Encontre o cristal que mais ressoa com sua energia e necessidades espirituais',
      hi: 'वह क्रिस्टल खोजें जो आपकी ऊर्जा और आध्यात्मिक आवश्यकताओं के साथ सबसे अधिक गूंजता है',
      ja: 'あなたのエネルギーとスピリチュアルなニーズに最も共鳴するクリスタルを見つける',
    },
    duration: '6 min',
    questions: 14,
    difficulty: 'Easy',
    category: 'Crystal',
    featured: false,
  },
];

const categories = ['All', 'Spiritual', 'Psychic', 'Energy', 'Crystal'];

export default function TestsPage({ params: { locale } }: TestsPageProps) {
  const t = useTranslations();

  const getHref = (href: string) => {
    return locale === 'en' ? href : `/${locale}${href}`;
  };

  const getLocalizedContent = (content: any) => {
    return content[locale] || content.en || '';
  };

  const featuredTests = mysticalTests.filter(test => test.featured);
  const allTests = mysticalTests;

  return (
    <PageLayout locale={locale}>
      <div className="min-h-screen bg-gradient-to-br from-cosmic-50 via-mystical-50 to-cosmic-100 dark:from-cosmic-950 dark:via-mystical-950 dark:to-cosmic-900">
        <div className="container mx-auto px-4 py-16">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="text-6xl mb-6">🧪</div>
            <h1 className="text-responsive-xl font-serif font-bold text-gradient mb-6">
              {t('tests.title')}
            </h1>
            <p className="text-responsive-md text-muted-foreground max-w-3xl mx-auto">
              {t('tests.description')}
            </p>
          </div>

          {/* Featured Tests */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-12">Featured Tests</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredTests.map((test) => (
                <Card key={test.id} variant="cosmic" hover className="overflow-hidden">
                  <CardHeader className="text-center">
                    <div className="text-4xl mb-4">{test.icon}</div>
                    <CardTitle className="text-xl">
                      {getLocalizedContent(test.title)}
                    </CardTitle>
                    <div className="flex justify-center space-x-4 text-sm text-muted-foreground">
                      <span>⏱️ {test.duration}</span>
                      <span>❓ {test.questions} questions</span>
                      <span>📊 {test.difficulty}</span>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-center mb-6">
                      {getLocalizedContent(test.description)}
                    </CardDescription>
                    <Link href={getHref(`/tests/${test.id}`)}>
                      <Button variant="cosmic" className="w-full">
                        {t('tests.take_test')}
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-2 mb-12">
            {categories.map((category) => (
              <button
                key={category}
                className="px-4 py-2 rounded-full text-sm font-medium transition-colors bg-muted text-muted-foreground hover:bg-accent hover:text-accent-foreground"
              >
                {category}
              </button>
            ))}
          </div>

          {/* All Tests */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-12">All Tests</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {allTests.map((test) => (
                <Card key={test.id} variant="mystical" hover className="overflow-hidden">
                  <CardHeader className="text-center pb-2">
                    <div className="text-3xl mb-2">{test.icon}</div>
                    <CardTitle className="text-lg leading-tight">
                      {getLocalizedContent(test.title)}
                    </CardTitle>
                    <div className="flex justify-center space-x-2 text-xs text-muted-foreground">
                      <span>{test.duration}</span>
                      <span>•</span>
                      <span>{test.questions}Q</span>
                      <span>•</span>
                      <span>{test.difficulty}</span>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <CardDescription className="text-sm mb-4 line-clamp-3">
                      {getLocalizedContent(test.description)}
                    </CardDescription>
                    <Link href={getHref(`/tests/${test.id}`)}>
                      <Button variant="outline" size="sm" className="w-full">
                        Start Test
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* How It Works */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-12">How It Works</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <Card variant="glass" className="text-center">
                <CardHeader>
                  <div className="text-4xl mb-4">1️⃣</div>
                  <CardTitle>Choose a Test</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Select from our collection of mystical assessments designed to reveal different aspects of your spiritual nature
                  </CardDescription>
                </CardContent>
              </Card>

              <Card variant="glass" className="text-center">
                <CardHeader>
                  <div className="text-4xl mb-4">2️⃣</div>
                  <CardTitle>Answer Honestly</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Respond to questions intuitively and honestly. Trust your first instinct for the most accurate results
                  </CardDescription>
                </CardContent>
              </Card>

              <Card variant="glass" className="text-center">
                <CardHeader>
                  <div className="text-4xl mb-4">3️⃣</div>
                  <CardTitle>Discover Insights</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Receive personalized results with detailed explanations and guidance for your spiritual journey
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center">
            <Card variant="mystical" className="max-w-2xl mx-auto">
              <CardHeader>
                <CardTitle className="text-2xl">Ready to Explore Your Mystical Nature?</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="mb-6">
                  Start with our most popular test and begin your journey of self-discovery
                </CardDescription>
                <Link href={getHref('/tests/aura-color')}>
                  <Button variant="cosmic" size="lg">
                    Discover Your Aura Color
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
