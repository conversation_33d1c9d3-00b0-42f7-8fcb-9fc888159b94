import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      event,
      timestamp,
      url,
      userAgent,
      ...parameters
    } = body;

    // 验证必需字段
    if (!event || !timestamp) {
      return NextResponse.json(
        { error: 'Missing required fields: event, timestamp' },
        { status: 400 }
      );
    }

    // 获取客户端信息
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    
    const referer = request.headers.get('referer');

    // 记录分析事件
    await prisma.analytics.create({
      data: {
        event,
        url: url || referer || '',
        userAgent: userAgent || request.headers.get('user-agent') || '',
        clientIP,
        parameters: parameters || {},
        timestamp: new Date(timestamp),
      },
    });

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Analytics event API error:', error);
    
    // 分析事件失败不应该影响用户体验，所以返回成功
    return NextResponse.json({ success: true });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const event = searchParams.get('event');
    const startDate = searchParams.get('start_date');
    const endDate = searchParams.get('end_date');
    const limit = parseInt(searchParams.get('limit') || '100');
    const offset = parseInt(searchParams.get('offset') || '0');

    // 构建查询条件
    const where: any = {};
    
    if (event) {
      where.event = event;
    }
    
    if (startDate && endDate) {
      where.timestamp = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    }

    // 获取事件数据
    const [events, total] = await Promise.all([
      prisma.analytics.findMany({
        where,
        orderBy: { timestamp: 'desc' },
        take: limit,
        skip: offset,
      }),
      prisma.analytics.count({ where }),
    ]);

    return NextResponse.json({
      events,
      total,
      limit,
      offset,
    });

  } catch (error) {
    console.error('Analytics events GET API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics events' },
      { status: 500 }
    );
  }
}
