import { useTranslations } from 'next-intl';
import { getTranslations } from 'next-intl/server';
import type { Metadata } from 'next';
import type { Locale } from '@/types';
import { PageLayout } from '@/components/layout/page-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { generateSEOMetadata } from '@/components/seo/seo-head';
import Link from 'next/link';

interface ProductsPageProps {
  params: { locale: Locale };
  searchParams: { category?: string; sort?: string };
}

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: Locale };
}): Promise<Metadata> {
  const t = await getTranslations({ locale, namespace: 'seo' });

  const seoData = {
    title: locale === 'zh' ? '神秘商品 - 神秘洞察' :
           locale === 'es' ? 'Productos Místicos - Perspectivas Místicas' :
           locale === 'pt' ? 'Produtos Místicos - Percepções Místicas' :
           locale === 'hi' ? 'रहस्यमय उत्पाद - रहस्यमय अंतर्दृष्टि' :
           locale === 'ja' ? 'ミスティカル商品 - ミスティカル・インサイト' :
           'Mystical Products - Mystical Insights',
    description: locale === 'zh' ? '探索我们精选的塔罗牌、水晶、占星工具和灵性书籍收藏' :
                locale === 'es' ? 'Explora nuestra colección curada de cartas del tarot, cristales, herramientas astrológicas y libros espirituales' :
                locale === 'pt' ? 'Explore nossa coleção curada de cartas de tarô, cristais, ferramentas astrológicas e livros espirituais' :
                locale === 'hi' ? 'हमारे क्यूरेटेड टैरो कार्ड, क्रिस्टल, ज्योतिषीय उपकरण और आध्यात्मिक पुस्तकों के संग्रह का अन्वेषण करें' :
                locale === 'ja' ? '厳選されたタロットカード、クリスタル、占星術ツール、スピリチュアル書籍のコレクションを探索' :
                'Explore our curated collection of tarot cards, crystals, astrological tools, and spiritual books',
    keywords: ['mystical products', 'tarot cards', 'crystals', 'spiritual books', 'astrology tools'],
    ogType: 'website' as const,
    twitterCard: 'summary_large_image' as const,
  };

  return generateSEOMetadata({
    seo: seoData,
    locale,
    path: '/products',
  });
}

// 模拟商品数据
const mockProducts = [
  {
    id: '1',
    slug: 'rider-waite-tarot-deck',
    name: {
      en: 'Rider-Waite Tarot Deck',
      zh: '韦特塔罗牌',
      es: 'Mazo de Tarot Rider-Waite',
      pt: 'Baralho de Tarô Rider-Waite',
      hi: 'राइडर-वेट टैरो डेक',
      ja: 'ライダー・ウェイト・タロットデッキ',
    },
    description: {
      en: 'The classic and most popular tarot deck for beginners and professionals',
      zh: '适合初学者和专业人士的经典最受欢迎塔罗牌',
      es: 'El mazo de tarot clásico y más popular para principiantes y profesionales',
      pt: 'O baralho de tarô clássico e mais popular para iniciantes e profissionais',
      hi: 'शुरुआती और पेशेवरों के लिए क्लासिक और सबसे लोकप्रिय टैरो डेक',
      ja: '初心者とプロのための古典的で最も人気のあるタロットデッキ',
    },
    price: 24.99,
    originalPrice: 29.99,
    currency: 'USD',
    category: 'Tarot Decks',
    image: '🃏',
    rating: 4.8,
    reviews: 156,
    inStock: true,
    featured: true,
  },
  {
    id: '2',
    slug: 'amethyst-crystal-set',
    name: {
      en: 'Amethyst Crystal Healing Set',
      zh: '紫水晶治疗套装',
      es: 'Set de Sanación con Cristales de Amatista',
      pt: 'Conjunto de Cura com Cristais de Ametista',
      hi: 'एमेथिस्ट क्रिस्टल हीलिंग सेट',
      ja: 'アメジストクリスタルヒーリングセット',
    },
    description: {
      en: 'Premium amethyst crystals for meditation, healing, and spiritual growth',
      zh: '用于冥想、治疗和灵性成长的优质紫水晶',
      es: 'Cristales de amatista premium para meditación, sanación y crecimiento espiritual',
      pt: 'Cristais de ametista premium para meditação, cura e crescimento espiritual',
      hi: 'ध्यान, उपचार और आध्यात्मिक विकास के लिए प्रीमियम एमेथिस्ट क्रिस्टल',
      ja: '瞑想、ヒーリング、スピリチュアルな成長のためのプレミアムアメジストクリスタル',
    },
    price: 39.99,
    currency: 'USD',
    category: 'Crystals',
    image: '💎',
    rating: 4.9,
    reviews: 89,
    inStock: true,
    featured: true,
  },
  {
    id: '3',
    slug: 'astrology-birth-chart-book',
    name: {
      en: 'Complete Guide to Birth Charts',
      zh: '出生图完整指南',
      es: 'Guía Completa de Cartas Natales',
      pt: 'Guia Completo de Mapas Natais',
      hi: 'जन्म कुंडली की पूर्ण गाइड',
      ja: '出生図の完全ガイド',
    },
    description: {
      en: 'Learn to read and interpret astrological birth charts like a professional',
      zh: '学习像专业人士一样阅读和解释占星出生图',
      es: 'Aprende a leer e interpretar cartas natales astrológicas como un profesional',
      pt: 'Aprenda a ler e interpretar mapas natais astrológicos como um profissional',
      hi: 'एक पेशेवर की तरह ज्योतिषीय जन्म कुंडली पढ़ना और व्याख्या करना सीखें',
      ja: 'プロのように占星術の出生図を読み、解釈することを学ぶ',
    },
    price: 19.99,
    currency: 'USD',
    category: 'Books',
    image: '📚',
    rating: 4.7,
    reviews: 234,
    inStock: true,
    featured: false,
  },
  {
    id: '4',
    slug: 'sage-cleansing-kit',
    name: {
      en: 'White Sage Cleansing Kit',
      zh: '白鼠尾草净化套装',
      es: 'Kit de Limpieza con Salvia Blanca',
      pt: 'Kit de Limpeza com Sálvia Branca',
      hi: 'व्हाइट सेज क्लींजिंग किट',
      ja: 'ホワイトセージクレンジングキット',
    },
    description: {
      en: 'Traditional sage bundle for spiritual cleansing and energy purification',
      zh: '用于灵性净化和能量净化的传统鼠尾草束',
      es: 'Manojo de salvia tradicional para limpieza espiritual y purificación energética',
      pt: 'Feixe de sálvia tradicional para limpeza espiritual e purificação energética',
      hi: 'आध्यात्मिक सफाई और ऊर्जा शुद्धिकरण के लिए पारंपरिक सेज बंडल',
      ja: 'スピリチュアルクレンジングとエネルギー浄化のための伝統的なセージバンドル',
    },
    price: 12.99,
    currency: 'USD',
    category: 'Spiritual Tools',
    image: '🌿',
    rating: 4.6,
    reviews: 78,
    inStock: true,
    featured: false,
  },
];

const categories = ['All', 'Tarot Decks', 'Crystals', 'Books', 'Spiritual Tools'];

export default function ProductsPage({ params: { locale }, searchParams }: ProductsPageProps) {
  const t = useTranslations();
  const selectedCategory = searchParams.category;
  const sortBy = searchParams.sort || 'featured';

  // 过滤商品
  const filteredProducts = selectedCategory && selectedCategory !== 'all'
    ? mockProducts.filter(product => product.category.toLowerCase().replace(' ', '-') === selectedCategory)
    : mockProducts;

  // 排序商品
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'rating':
        return b.rating - a.rating;
      case 'newest':
        return b.id.localeCompare(a.id);
      default:
        return b.featured ? 1 : -1;
    }
  });

  const getHref = (href: string) => {
    return locale === 'en' ? href : `/${locale}${href}`;
  };

  const getLocalizedContent = (content: any) => {
    return content[locale] || content.en || '';
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
    }).format(price);
  };

  return (
    <PageLayout locale={locale}>
      <div className="min-h-screen bg-gradient-to-br from-background via-muted/20 to-background">
        <div className="container mx-auto px-4 py-16">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-responsive-xl font-serif font-bold text-gradient mb-6">
              {t('products.title')}
            </h1>
            <p className="text-responsive-md text-muted-foreground max-w-3xl mx-auto">
              Discover our carefully curated collection of mystical tools, crystals, and spiritual guides
            </p>
          </div>

          {/* Filters */}
          <div className="flex flex-col md:flex-row justify-between items-center mb-8 gap-4">
            {/* Category Filter */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Link
                  key={category}
                  href={getHref(`/products${category !== 'All' ? `?category=${category.toLowerCase().replace(' ', '-')}` : ''}`)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                    (category === 'All' && !selectedCategory) ||
                    category.toLowerCase().replace(' ', '-') === selectedCategory
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                  }`}
                >
                  {category}
                </Link>
              ))}
            </div>

            {/* Sort Filter */}
            <select
              className="px-4 py-2 rounded-md border border-border bg-background text-foreground"
              value={sortBy}
              onChange={(e) => {
                const params = new URLSearchParams(searchParams);
                params.set('sort', e.target.value);
                window.history.pushState(null, '', `?${params.toString()}`);
              }}
            >
              <option value="featured">Featured</option>
              <option value="newest">Newest</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="rating">Highest Rated</option>
            </select>
          </div>

          {/* Featured Products */}
          {!selectedCategory && (
            <div className="mb-16">
              <h2 className="text-2xl font-bold mb-8">{t('products.featured')}</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {mockProducts
                  .filter(product => product.featured)
                  .map((product) => (
                    <Card key={product.id} variant="mystical" hover className="overflow-hidden">
                      <div className="aspect-square bg-gradient-to-br from-mystical-100 to-cosmic-100 flex items-center justify-center">
                        <span className="text-6xl">{product.image}</span>
                      </div>
                      <CardHeader>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-muted-foreground">{product.category}</span>
                          <div className="flex items-center text-sm">
                            <span className="text-yellow-500">⭐</span>
                            <span className="ml-1">{product.rating}</span>
                            <span className="text-muted-foreground ml-1">({product.reviews})</span>
                          </div>
                        </div>
                        <CardTitle className="text-lg">
                          {getLocalizedContent(product.name)}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <CardDescription className="mb-4">
                          {getLocalizedContent(product.description)}
                        </CardDescription>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <span className="text-lg font-bold text-primary">
                              {formatPrice(product.price, product.currency)}
                            </span>
                            {product.originalPrice && (
                              <span className="text-sm text-muted-foreground line-through">
                                {formatPrice(product.originalPrice, product.currency)}
                              </span>
                            )}
                          </div>
                          <Link href={getHref(`/products/${product.slug}`)}>
                            <Button variant="outline" size="sm">
                              View Details
                            </Button>
                          </Link>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
              </div>
            </div>
          )}

          {/* All Products */}
          <div>
            <h2 className="text-2xl font-bold mb-8">
              {selectedCategory ? `${selectedCategory.replace('-', ' ')} Products` : 'All Products'}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {sortedProducts.map((product) => (
                <Card key={product.id} variant="mystical" hover className="overflow-hidden">
                  <div className="aspect-square bg-gradient-to-br from-mystical-100 to-cosmic-100 flex items-center justify-center">
                    <span className="text-4xl">{product.image}</span>
                  </div>
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between text-xs text-muted-foreground mb-1">
                      <span>{product.category}</span>
                      <div className="flex items-center">
                        <span className="text-yellow-500">⭐</span>
                        <span className="ml-1">{product.rating}</span>
                      </div>
                    </div>
                    <CardTitle className="text-base leading-tight">
                      {getLocalizedContent(product.name)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex items-center justify-between mb-3">
                      <span className="font-bold text-primary">
                        {formatPrice(product.price, product.currency)}
                      </span>
                      {product.inStock ? (
                        <span className="text-xs text-green-600">{t('products.in_stock')}</span>
                      ) : (
                        <span className="text-xs text-red-600">{t('products.out_of_stock')}</span>
                      )}
                    </div>
                    <Link href={getHref(`/products/${product.slug}`)}>
                      <Button variant="outline" size="sm" className="w-full">
                        View Product
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-16">
            <Card variant="glass" className="max-w-2xl mx-auto">
              <CardHeader>
                <CardTitle className="text-2xl">Can't Find What You're Looking For?</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="mb-6">
                  Contact our mystical experts for personalized product recommendations
                </CardDescription>
                <Link href={getHref('/contact')}>
                  <Button variant="mystical" size="lg">
                    Get Personalized Help
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
