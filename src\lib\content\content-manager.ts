import { prisma } from '@/lib/prisma';
import { cache } from '@/lib/cache';
import type { Locale } from '@/types';

// 内容类型
export type ContentType = 'article' | 'product' | 'test' | 'page';

// 内容状态
export type ContentStatus = 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';

// 多语言内容接口
export interface MultilingualContent {
  [key: string]: string;
}

// 文章内容接口
export interface ArticleContent {
  id: string;
  slug: string;
  title: MultilingualContent;
  content: MultilingualContent;
  excerpt?: MultilingualContent;
  coverImage?: string;
  authorId: string;
  categoryId: string;
  status: ContentStatus;
  publishedAt?: Date;
  readingTime: number;
  views: number;
  featured: boolean;
  seoTitle?: MultilingualContent;
  seoDescription?: MultilingualContent;
  seoKeywords?: MultilingualContent;
  createdAt: Date;
  updatedAt: Date;
}

// 商品内容接口
export interface ProductContent {
  id: string;
  slug: string;
  name: MultilingualContent;
  description: MultilingualContent;
  price: number;
  currency: string;
  images: string[];
  categoryId: string;
  status: 'ACTIVE' | 'INACTIVE' | 'OUT_OF_STOCK';
  inventory: number;
  sku: string;
  featured: boolean;
  specifications?: any;
  seoTitle?: MultilingualContent;
  seoDescription?: MultilingualContent;
  createdAt: Date;
  updatedAt: Date;
}

// 内容管理器类
export class ContentManager {
  // 获取文章列表
  static async getArticles(options: {
    locale?: Locale;
    category?: string;
    status?: ContentStatus;
    featured?: boolean;
    limit?: number;
    offset?: number;
  } = {}): Promise<{ articles: ArticleContent[]; total: number }> {
    const {
      locale,
      category,
      status = 'PUBLISHED',
      featured,
      limit = 10,
      offset = 0,
    } = options;

    // 构建查询条件
    const where: any = { status };
    if (category) {
      where.category = { slug: category };
    }
    if (featured !== undefined) {
      where.featured = featured;
    }

    // 生成缓存键
    const cacheKey = `articles:${JSON.stringify({ locale, category, status, featured, limit, offset })}`;
    
    // 尝试从缓存获取
    const cached = await cache.get(cacheKey);
    if (cached) {
      return cached;
    }

    // 从数据库查询
    const [articles, total] = await Promise.all([
      prisma.article.findMany({
        where,
        include: {
          author: {
            select: { id: true, name: true, email: true },
          },
          category: true,
          tags: true,
        },
        orderBy: { publishedAt: 'desc' },
        take: limit,
        skip: offset,
      }),
      prisma.article.count({ where }),
    ]);

    const result = { articles, total };
    
    // 缓存结果
    await cache.set(cacheKey, result, 300); // 5分钟缓存
    
    return result;
  }

  // 获取单篇文章
  static async getArticle(slug: string, locale?: Locale): Promise<ArticleContent | null> {
    const cacheKey = `article:${slug}:${locale || 'all'}`;
    
    // 尝试从缓存获取
    const cached = await cache.get(cacheKey);
    if (cached) {
      return cached;
    }

    // 从数据库查询
    const article = await prisma.article.findUnique({
      where: { slug },
      include: {
        author: {
          select: { id: true, name: true, email: true },
        },
        category: true,
        tags: true,
      },
    });

    if (!article) return null;

    // 增加浏览量
    await prisma.article.update({
      where: { id: article.id },
      data: { views: { increment: 1 } },
    });

    // 缓存结果
    await cache.set(cacheKey, article, 3600); // 1小时缓存
    
    return article;
  }

  // 创建文章
  static async createArticle(data: Omit<ArticleContent, 'id' | 'createdAt' | 'updatedAt' | 'views'>): Promise<ArticleContent> {
    const article = await prisma.article.create({
      data: {
        ...data,
        views: 0,
      },
      include: {
        author: {
          select: { id: true, name: true, email: true },
        },
        category: true,
        tags: true,
      },
    });

    // 清除相关缓存
    await this.invalidateArticleCache();

    return article;
  }

  // 更新文章
  static async updateArticle(id: string, data: Partial<ArticleContent>): Promise<ArticleContent> {
    const article = await prisma.article.update({
      where: { id },
      data,
      include: {
        author: {
          select: { id: true, name: true, email: true },
        },
        category: true,
        tags: true,
      },
    });

    // 清除相关缓存
    await this.invalidateArticleCache(article.slug);

    return article;
  }

  // 删除文章
  static async deleteArticle(id: string): Promise<void> {
    const article = await prisma.article.findUnique({
      where: { id },
      select: { slug: true },
    });

    await prisma.article.delete({
      where: { id },
    });

    // 清除相关缓存
    if (article) {
      await this.invalidateArticleCache(article.slug);
    }
  }

  // 获取商品列表
  static async getProducts(options: {
    locale?: Locale;
    category?: string;
    status?: 'ACTIVE' | 'INACTIVE' | 'OUT_OF_STOCK';
    featured?: boolean;
    limit?: number;
    offset?: number;
  } = {}): Promise<{ products: ProductContent[]; total: number }> {
    const {
      locale,
      category,
      status = 'ACTIVE',
      featured,
      limit = 10,
      offset = 0,
    } = options;

    // 构建查询条件
    const where: any = { status };
    if (category) {
      where.category = { slug: category };
    }
    if (featured !== undefined) {
      where.featured = featured;
    }

    // 生成缓存键
    const cacheKey = `products:${JSON.stringify({ locale, category, status, featured, limit, offset })}`;
    
    // 尝试从缓存获取
    const cached = await cache.get(cacheKey);
    if (cached) {
      return cached;
    }

    // 从数据库查询
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        include: {
          category: true,
          tags: true,
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
      }),
      prisma.product.count({ where }),
    ]);

    const result = { products, total };
    
    // 缓存结果
    await cache.set(cacheKey, result, 600); // 10分钟缓存
    
    return result;
  }

  // 获取单个商品
  static async getProduct(slug: string, locale?: Locale): Promise<ProductContent | null> {
    const cacheKey = `product:${slug}:${locale || 'all'}`;
    
    // 尝试从缓存获取
    const cached = await cache.get(cacheKey);
    if (cached) {
      return cached;
    }

    // 从数据库查询
    const product = await prisma.product.findUnique({
      where: { slug },
      include: {
        category: true,
        tags: true,
      },
    });

    if (!product) return null;

    // 缓存结果
    await cache.set(cacheKey, product, 3600); // 1小时缓存
    
    return product;
  }

  // 获取分类列表
  static async getCategories(type: 'article' | 'product' = 'article'): Promise<any[]> {
    const cacheKey = `categories:${type}`;
    
    // 尝试从缓存获取
    const cached = await cache.get(cacheKey);
    if (cached) {
      return cached;
    }

    // 从数据库查询
    const categories = type === 'article' 
      ? await prisma.category.findMany({
          orderBy: { name: 'asc' },
        })
      : await prisma.productCategory.findMany({
          orderBy: { name: 'asc' },
        });

    // 缓存结果
    await cache.set(cacheKey, categories, 3600); // 1小时缓存
    
    return categories;
  }

  // 获取标签列表
  static async getTags(): Promise<any[]> {
    const cacheKey = 'tags:all';
    
    // 尝试从缓存获取
    const cached = await cache.get(cacheKey);
    if (cached) {
      return cached;
    }

    // 从数据库查询
    const tags = await prisma.tag.findMany({
      orderBy: { name: 'asc' },
    });

    // 缓存结果
    await cache.set(cacheKey, tags, 3600); // 1小时缓存
    
    return tags;
  }

  // 搜索内容
  static async searchContent(
    query: string,
    type: ContentType = 'article',
    locale?: Locale,
    limit: number = 10
  ): Promise<any[]> {
    const cacheKey = `search:${type}:${query}:${locale}:${limit}`;
    
    // 尝试从缓存获取
    const cached = await cache.get(cacheKey);
    if (cached) {
      return cached;
    }

    let results: any[] = [];

    if (type === 'article') {
      results = await prisma.article.findMany({
        where: {
          OR: [
            { title: { path: [locale || 'en'], string_contains: query } },
            { content: { path: [locale || 'en'], string_contains: query } },
            { excerpt: { path: [locale || 'en'], string_contains: query } },
          ],
          status: 'PUBLISHED',
        },
        include: {
          author: {
            select: { id: true, name: true },
          },
          category: true,
        },
        take: limit,
        orderBy: { publishedAt: 'desc' },
      });
    } else if (type === 'product') {
      results = await prisma.product.findMany({
        where: {
          OR: [
            { name: { path: [locale || 'en'], string_contains: query } },
            { description: { path: [locale || 'en'], string_contains: query } },
          ],
          status: 'ACTIVE',
        },
        include: {
          category: true,
        },
        take: limit,
        orderBy: { createdAt: 'desc' },
      });
    }

    // 缓存结果
    await cache.set(cacheKey, results, 300); // 5分钟缓存
    
    return results;
  }

  // 清除文章缓存
  static async invalidateArticleCache(slug?: string): Promise<void> {
    if (slug) {
      await cache.clear(`article:${slug}:*`);
    }
    await cache.clear('articles:*');
  }

  // 清除商品缓存
  static async invalidateProductCache(slug?: string): Promise<void> {
    if (slug) {
      await cache.clear(`product:${slug}:*`);
    }
    await cache.clear('products:*');
  }

  // 清除所有内容缓存
  static async invalidateAllCache(): Promise<void> {
    await Promise.all([
      cache.clear('articles:*'),
      cache.clear('products:*'),
      cache.clear('categories:*'),
      cache.clear('tags:*'),
      cache.clear('search:*'),
    ]);
  }
}

export default ContentManager;
