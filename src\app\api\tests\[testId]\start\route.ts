import { NextRequest, NextResponse } from 'next/server';
import { TestFlowManager } from '@/lib/tests/test-flow-manager';
import { z } from 'zod';
import type { Locale } from '@/types';

const startTestSchema = z.object({
  userId: z.string().optional(),
  sessionId: z.string().optional(),
  locale: z.enum(['en', 'zh', 'es', 'pt', 'hi', 'ja']).optional().default('en'),
});

// POST /api/tests/[testId]/start - 开始测试
export async function POST(
  request: NextRequest,
  { params }: { params: { testId: string } }
) {
  try {
    const { testId } = params;
    const body = await request.json();
    const { userId, sessionId, locale } = startTestSchema.parse(body);

    const testFlowManager = new TestFlowManager();
    const testSessionId = await testFlowManager.startTestSession(
      testId,
      userId,
      sessionId,
      locale as Locale
    );

    return NextResponse.json({
      success: true,
      sessionId: testSessionId,
      message: 'Test session started successfully',
    });
  } catch (error) {
    console.error('Error starting test:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to start test',
      },
      { status: 500 }
    );
  }
}
