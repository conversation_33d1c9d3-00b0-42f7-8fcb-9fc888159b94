import { NextResponse } from 'next/server';
import { locales, getLocalePath } from '@/i18n';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    
    // 静态页面
    const staticPages = [
      '',
      '/blog',
      '/tarot',
      '/astrology',
      '/numerology',
      '/tests',
      '/products',
      '/about',
      '/contact',
      '/privacy',
      '/terms',
    ];

    // 获取动态内容
    const [articles, products, tests] = await Promise.all([
      prisma.article.findMany({
        where: { status: 'PUBLISHED' },
        select: { slug: true, updatedAt: true },
      }),
      prisma.product.findMany({
        where: { status: 'ACTIVE' },
        select: { slug: true, updatedAt: true },
      }),
      prisma.mysticalTest.findMany({
        select: { slug: true, updatedAt: true },
      }),
    ]);

    // 生成XML
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">
${generateStaticUrls(baseUrl, staticPages)}
${generateDynamicUrls(baseUrl, 'blog', articles)}
${generateDynamicUrls(baseUrl, 'products', products)}
${generateDynamicUrls(baseUrl, 'tests', tests)}
</urlset>`;

    return new NextResponse(sitemap, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600',
      },
    });
  } catch (error) {
    console.error('Sitemap generation error:', error);
    return new NextResponse('Error generating sitemap', { status: 500 });
  }
}

function generateStaticUrls(baseUrl: string, pages: string[]): string {
  return pages
    .map(page => {
      return locales
        .map(locale => {
          const url = `${baseUrl}${getLocalePath(locale, page)}`;
          const alternates = locales
            .map(altLocale => {
              const altUrl = `${baseUrl}${getLocalePath(altLocale, page)}`;
              return `    <xhtml:link rel="alternate" hreflang="${altLocale}" href="${altUrl}" />`;
            })
            .join('\n');

          return `  <url>
    <loc>${url}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>${page === '' ? '1.0' : '0.8'}</priority>
${alternates}
  </url>`;
        })
        .join('\n');
    })
    .join('\n');
}

function generateDynamicUrls(
  baseUrl: string,
  category: string,
  items: Array<{ slug: string; updatedAt: Date }>
): string {
  return items
    .map(item => {
      return locales
        .map(locale => {
          const url = `${baseUrl}${getLocalePath(locale, `/${category}/${item.slug}`)}`;
          const alternates = locales
            .map(altLocale => {
              const altUrl = `${baseUrl}${getLocalePath(altLocale, `/${category}/${item.slug}`)}`;
              return `    <xhtml:link rel="alternate" hreflang="${altLocale}" href="${altUrl}" />`;
            })
            .join('\n');

          return `  <url>
    <loc>${url}</loc>
    <lastmod>${item.updatedAt.toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
${alternates}
  </url>`;
        })
        .join('\n');
    })
    .join('\n');
}
