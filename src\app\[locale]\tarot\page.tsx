import { useTranslations } from 'next-intl';
import { getTranslations } from 'next-intl/server';
import type { Metadata } from 'next';
import type { Locale } from '@/types';
import { PageLayout } from '@/components/layout/page-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { generateSEOMetadata, seoTemplates } from '@/components/seo/seo-head';
import Link from 'next/link';

interface TarotPageProps {
  params: { locale: Locale };
}

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: Locale };
}): Promise<Metadata> {
  const seoData = seoTemplates.tarot(locale);
  return generateSEOMetadata({
    seo: seoData,
    locale,
    path: '/tarot',
  });
}

// 塔罗服务数据
const tarotServices = [
  {
    id: 'daily',
    icon: '🌅',
    title: {
      en: 'Daily Reading',
      zh: '每日占卜',
      es: 'Lectura Diaria',
      pt: 'Leitura Diária',
      hi: 'दैनिक रीडिंग',
      ja: 'デイリーリーディング',
    },
    description: {
      en: 'Get insights for your day with a single card reading',
      zh: '通过单张卡片解读获得今日洞察',
      es: 'Obtén perspectivas para tu día con una lectura de una sola carta',
      pt: 'Obtenha insights para o seu dia com uma leitura de carta única',
      hi: 'एक कार्ड रीडिंग के साथ अपने दिन के लिए अंतर्दृष्टि प्राप्त करें',
      ja: '1枚のカードリーディングで今日の洞察を得る',
    },
    duration: '5 min',
    price: 'Free',
    href: '/tarot/daily',
  },
  {
    id: 'love',
    icon: '💕',
    title: {
      en: 'Love Reading',
      zh: '爱情占卜',
      es: 'Lectura de Amor',
      pt: 'Leitura do Amor',
      hi: 'प्रेम रीडिंग',
      ja: '恋愛リーディング',
    },
    description: {
      en: 'Explore your romantic relationships and love life',
      zh: '探索你的浪漫关系和爱情生活',
      es: 'Explora tus relaciones románticas y vida amorosa',
      pt: 'Explore seus relacionamentos românticos e vida amorosa',
      hi: 'अपने रोमांटिक रिश्तों और प्रेम जीवन का अन्वेषण करें',
      ja: 'あなたの恋愛関係と愛の人生を探求する',
    },
    duration: '10 min',
    price: '$5',
    href: '/tarot/love',
  },
  {
    id: 'career',
    icon: '💼',
    title: {
      en: 'Career Reading',
      zh: '事业占卜',
      es: 'Lectura de Carrera',
      pt: 'Leitura de Carreira',
      hi: 'करियर रीडिंग',
      ja: 'キャリアリーディング',
    },
    description: {
      en: 'Discover guidance for your professional path and career decisions',
      zh: '为你的职业道路和事业决策寻找指导',
      es: 'Descubre orientación para tu camino profesional y decisiones de carrera',
      pt: 'Descubra orientação para seu caminho profissional e decisões de carreira',
      hi: 'अपने पेशेवर पथ और करियर निर्णयों के लिए मार्गदर्शन खोजें',
      ja: 'あなたの職業的な道とキャリアの決断のためのガイダンスを発見する',
    },
    duration: '15 min',
    price: '$8',
    href: '/tarot/career',
  },
  {
    id: 'general',
    icon: '🔮',
    title: {
      en: 'General Reading',
      zh: '综合占卜',
      es: 'Lectura General',
      pt: 'Leitura Geral',
      hi: 'सामान्य रीडिंग',
      ja: '総合リーディング',
    },
    description: {
      en: 'Comprehensive guidance covering all aspects of your life',
      zh: '涵盖你生活各个方面的综合指导',
      es: 'Orientación integral que cubre todos los aspectos de tu vida',
      pt: 'Orientação abrangente cobrindo todos os aspectos da sua vida',
      hi: 'आपके जीवन के सभी पहलुओं को कवर करने वाला व्यापक मार्गदर्शन',
      ja: 'あなたの人生のすべての側面をカバーする包括的なガイダンス',
    },
    duration: '20 min',
    price: '$12',
    href: '/tarot/general',
  },
];

// 塔罗牌组介绍
const tarotDecks = [
  {
    name: 'Rider-Waite',
    description: 'The most popular and widely recognized tarot deck',
    image: '🃏',
  },
  {
    name: 'Thoth Tarot',
    description: 'Created by Aleister Crowley, known for its esoteric symbolism',
    image: '🎴',
  },
  {
    name: 'Marseille Tarot',
    description: 'Traditional French tarot with historical significance',
    image: '🂠',
  },
];

export default function TarotPage({ params: { locale } }: TarotPageProps) {
  const t = useTranslations();

  const getHref = (href: string) => {
    return locale === 'en' ? href : `/${locale}${href}`;
  };

  const getLocalizedContent = (content: any) => {
    return content[locale] || content.en || '';
  };

  return (
    <PageLayout locale={locale}>
      <div className="min-h-screen bg-gradient-to-br from-mystical-50 via-cosmic-50 to-mystical-100 dark:from-mystical-950 dark:via-cosmic-950 dark:to-mystical-900">
        <div className="container mx-auto px-4 py-16">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <div className="text-6xl mb-6">🔮</div>
            <h1 className="text-responsive-xl font-serif font-bold text-gradient mb-6">
              {t('tarot.title')}
            </h1>
            <p className="text-responsive-md text-muted-foreground max-w-3xl mx-auto mb-8">
              {t('tarot.description')}
            </p>
            <Button variant="mystical" size="lg">
              Start Your Reading
            </Button>
          </div>

          {/* Tarot Services */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-12">Choose Your Reading</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {tarotServices.map((service) => (
                <Card key={service.id} variant="mystical" hover className="text-center">
                  <CardHeader>
                    <div className="text-4xl mb-4">{service.icon}</div>
                    <CardTitle className="text-xl">
                      {getLocalizedContent(service.title)}
                    </CardTitle>
                    <div className="flex justify-between text-sm text-muted-foreground">
                      <span>{service.duration}</span>
                      <span className="font-semibold text-primary">{service.price}</span>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="mb-6">
                      {getLocalizedContent(service.description)}
                    </CardDescription>
                    <Link href={getHref(service.href)}>
                      <Button variant="outline" className="w-full">
                        Start Reading
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Tarot Card Categories */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-12">Explore Tarot Cards</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card variant="cosmic" hover className="text-center">
                <CardHeader>
                  <div className="text-3xl mb-2">👑</div>
                  <CardTitle>{t('tarot.cards.major_arcana')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    The 22 major life themes and spiritual lessons
                  </CardDescription>
                </CardContent>
              </Card>

              <Card variant="cosmic" hover className="text-center">
                <CardHeader>
                  <div className="text-3xl mb-2">🏆</div>
                  <CardTitle>{t('tarot.cards.cups')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Emotions, relationships, and intuition
                  </CardDescription>
                </CardContent>
              </Card>

              <Card variant="cosmic" hover className="text-center">
                <CardHeader>
                  <div className="text-3xl mb-2">🔥</div>
                  <CardTitle>{t('tarot.cards.wands')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Creativity, passion, and career
                  </CardDescription>
                </CardContent>
              </Card>

              <Card variant="cosmic" hover className="text-center">
                <CardHeader>
                  <div className="text-3xl mb-2">⚔️</div>
                  <CardTitle>{t('tarot.cards.swords')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Thoughts, communication, and challenges
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Popular Tarot Decks */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-12">Popular Tarot Decks</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {tarotDecks.map((deck, index) => (
                <Card key={index} variant="mystical" hover>
                  <CardHeader className="text-center">
                    <div className="text-4xl mb-4">{deck.image}</div>
                    <CardTitle>{deck.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-center">
                      {deck.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center">
            <Card variant="glass" className="max-w-2xl mx-auto">
              <CardHeader>
                <CardTitle className="text-2xl">Ready to Discover Your Path?</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="mb-6">
                  Start your tarot journey today and unlock the wisdom of the cards
                </CardDescription>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href={getHref('/tarot/daily')}>
                    <Button variant="mystical" size="lg">
                      Free Daily Reading
                    </Button>
                  </Link>
                  <Link href={getHref('/blog?category=tarot')}>
                    <Button variant="outline" size="lg">
                      Learn More
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
