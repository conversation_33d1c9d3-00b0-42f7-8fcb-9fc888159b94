import * as React from 'react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import type { Locale } from '@/types';

interface FooterProps {
  locale: Locale;
  className?: string;
}

export function Footer({ locale, className }: FooterProps) {
  const t = useTranslations();

  const getHref = (href: string) => {
    return locale === 'en' ? href : `/${locale}${href}`;
  };

  const quickLinks = [
    { key: 'home', href: '/' },
    { key: 'blog', href: '/blog' },
    { key: 'about', href: '/about' },
    { key: 'contact', href: '/contact' },
  ];

  const services = [
    { key: 'tarot', href: '/tarot' },
    { key: 'astrology', href: '/astrology' },
    { key: 'numerology', href: '/numerology' },
    { key: 'tests', href: '/tests' },
  ];

  const support = [
    { key: 'privacy', href: '/privacy' },
    { key: 'terms', href: '/terms' },
    { key: 'contact', href: '/contact' },
  ];

  return (
    <footer className={cn('bg-muted/30 border-t border-border/50', className)}>
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-mystical-500 to-cosmic-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg font-bold">M</span>
              </div>
              <h3 className="text-lg font-bold bg-gradient-to-r from-mystical-600 to-cosmic-600 bg-clip-text text-transparent">
                {locale === 'zh' ? '神秘洞察' : 
                 locale === 'es' ? 'Perspectivas Místicas' :
                 locale === 'pt' ? 'Percepções Místicas' :
                 locale === 'hi' ? 'रहस्यमय अंतर्दृष्टि' :
                 locale === 'ja' ? 'ミスティカル・インサイト' :
                 'Mystical Insights'}
              </h3>
            </div>
            <p className="text-sm text-muted-foreground max-w-xs">
              {t('footer.description')}
            </p>
            <div className="flex space-x-2">
              <a href="#" aria-label="Facebook" className="inline-flex items-center justify-center w-10 h-10 rounded-md text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground">
                📘
              </a>
              <a href="#" aria-label="Twitter" className="inline-flex items-center justify-center w-10 h-10 rounded-md text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground">
                🐦
              </a>
              <a href="#" aria-label="Instagram" className="inline-flex items-center justify-center w-10 h-10 rounded-md text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground">
                📷
              </a>
              <a href="#" aria-label="YouTube" className="inline-flex items-center justify-center w-10 h-10 rounded-md text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground">
                📺
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold uppercase tracking-wider">
              {t('footer.quick_links')}
            </h4>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.key}>
                  <Link
                    href={getHref(link.href)}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {t(`navigation.${link.key}`)}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold uppercase tracking-wider">
              {t('footer.services')}
            </h4>
            <ul className="space-y-2">
              {services.map((service) => (
                <li key={service.key}>
                  <Link
                    href={getHref(service.href)}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {t(`navigation.${service.key}`)}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Newsletter */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold uppercase tracking-wider">
              {t('footer.newsletter')}
            </h4>
            <p className="text-sm text-muted-foreground">
              {t('footer.newsletter_description')}
            </p>
            <div className="space-y-2">
              <input
                type="email"
                placeholder={t('footer.email_placeholder')}
                className="w-full px-3 py-2 text-sm bg-background border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              />
              <Button variant="mystical" size="sm" className="w-full">
                {t('footer.subscribe')}
              </Button>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-8 pt-8 border-t border-border/50">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-sm text-muted-foreground">
              {t('footer.copyright')}
            </p>
            <div className="flex space-x-4">
              {support.map((link) => (
                <Link
                  key={link.key}
                  href={getHref(link.href)}
                  className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                >
                  {t(`common.${link.key}`)}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}

export default Footer;
