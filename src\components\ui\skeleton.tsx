import * as React from 'react';
import { cn } from '@/lib/utils';

export interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'circular' | 'rectangular' | 'text';
  animation?: 'pulse' | 'wave' | 'none';
}

const Skeleton = React.forwardRef<HTMLDivElement, SkeletonProps>(
  ({ className, variant = 'default', animation = 'pulse', ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'bg-muted',
          {
            'animate-pulse': animation === 'pulse',
            'animate-wave': animation === 'wave',
            'rounded-md': variant === 'default' || variant === 'rectangular',
            'rounded-full': variant === 'circular',
            'h-4': variant === 'text',
          },
          className
        )}
        {...props}
      />
    );
  }
);

Skeleton.displayName = 'Skeleton';

// 预定义的骨架屏组件
const SkeletonCard: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn('space-y-3', className)}>
    <Skeleton className="h-48 w-full" variant="rectangular" />
    <div className="space-y-2">
      <Skeleton className="h-4 w-3/4" variant="text" />
      <Skeleton className="h-4 w-1/2" variant="text" />
    </div>
  </div>
);

const SkeletonAvatar: React.FC<{ size?: 'sm' | 'md' | 'lg'; className?: string }> = ({ 
  size = 'md', 
  className 
}) => {
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-12 w-12',
    lg: 'h-16 w-16',
  };

  return (
    <Skeleton 
      className={cn(sizeClasses[size], className)} 
      variant="circular" 
    />
  );
};

const SkeletonText: React.FC<{ 
  lines?: number; 
  className?: string;
  lastLineWidth?: string;
}> = ({ 
  lines = 3, 
  className,
  lastLineWidth = 'w-3/4'
}) => (
  <div className={cn('space-y-2', className)}>
    {Array.from({ length: lines }).map((_, i) => (
      <Skeleton
        key={i}
        className={cn(
          'h-4',
          i === lines - 1 ? lastLineWidth : 'w-full'
        )}
        variant="text"
      />
    ))}
  </div>
);

const SkeletonList: React.FC<{ 
  items?: number; 
  className?: string;
}> = ({ items = 5, className }) => (
  <div className={cn('space-y-4', className)}>
    {Array.from({ length: items }).map((_, i) => (
      <div key={i} className="flex items-center space-x-4">
        <SkeletonAvatar size="sm" />
        <div className="flex-1 space-y-2">
          <Skeleton className="h-4 w-1/4" variant="text" />
          <Skeleton className="h-3 w-3/4" variant="text" />
        </div>
      </div>
    ))}
  </div>
);

const SkeletonBlog: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn('space-y-6', className)}>
    <Skeleton className="h-64 w-full" variant="rectangular" />
    <div className="space-y-4">
      <Skeleton className="h-8 w-3/4" variant="text" />
      <SkeletonText lines={4} />
      <div className="flex items-center space-x-4">
        <SkeletonAvatar size="sm" />
        <div className="space-y-1">
          <Skeleton className="h-3 w-20" variant="text" />
          <Skeleton className="h-3 w-16" variant="text" />
        </div>
      </div>
    </div>
  </div>
);

export { 
  Skeleton, 
  SkeletonCard, 
  SkeletonAvatar, 
  SkeletonText, 
  SkeletonList,
  SkeletonBlog 
};
