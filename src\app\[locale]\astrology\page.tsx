import { useTranslations } from 'next-intl';
import { getTranslations } from 'next-intl/server';
import type { Metadata } from 'next';
import type { Locale } from '@/types';
import { PageLayout } from '@/components/layout/page-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { generateSEOMetadata, seoTemplates } from '@/components/seo/seo-head';
import Link from 'next/link';

interface AstrologyPageProps {
  params: { locale: Locale };
}

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: Locale };
}): Promise<Metadata> {
  const seoData = seoTemplates.astrology(locale);
  return generateSEOMetadata({
    seo: seoData,
    locale,
    path: '/astrology',
  });
}

// 星座数据
const zodiacSigns = [
  { sign: 'aries', emoji: '♈', dates: 'Mar 21 - Apr 19', element: 'Fire' },
  { sign: 'taurus', emoji: '♉', dates: 'Apr 20 - May 20', element: 'Earth' },
  { sign: 'gemini', emoji: '♊', dates: 'May 21 - Jun 20', element: 'Air' },
  { sign: 'cancer', emoji: '♋', dates: 'Jun 21 - Jul 22', element: 'Water' },
  { sign: 'leo', emoji: '♌', dates: 'Jul 23 - Aug 22', element: 'Fire' },
  { sign: 'virgo', emoji: '♍', dates: 'Aug 23 - Sep 22', element: 'Earth' },
  { sign: 'libra', emoji: '♎', dates: 'Sep 23 - Oct 22', element: 'Air' },
  { sign: 'scorpio', emoji: '♏', dates: 'Oct 23 - Nov 21', element: 'Water' },
  { sign: 'sagittarius', emoji: '♐', dates: 'Nov 22 - Dec 21', element: 'Fire' },
  { sign: 'capricorn', emoji: '♑', dates: 'Dec 22 - Jan 19', element: 'Earth' },
  { sign: 'aquarius', emoji: '♒', dates: 'Jan 20 - Feb 18', element: 'Air' },
  { sign: 'pisces', emoji: '♓', dates: 'Feb 19 - Mar 20', element: 'Water' },
];

// 占星服务
const astrologyServices = [
  {
    id: 'daily',
    icon: '🌅',
    title: {
      en: 'Daily Horoscope',
      zh: '每日运势',
      es: 'Horóscopo Diario',
      pt: 'Horóscopo Diário',
      hi: 'दैनिक राशिफल',
      ja: '今日の運勢',
    },
    description: {
      en: 'Get your personalized daily astrological forecast',
      zh: '获取你的个性化每日星象预测',
      es: 'Obtén tu pronóstico astrológico diario personalizado',
      pt: 'Obtenha sua previsão astrológica diária personalizada',
      hi: 'अपना व्यक्तिगत दैनिक ज्योतिषीय पूर्वानुमान प्राप्त करें',
      ja: 'あなたの個人的な毎日の占星術予報を取得',
    },
    href: '/astrology/daily',
  },
  {
    id: 'natal',
    icon: '🌟',
    title: {
      en: 'Natal Chart',
      zh: '本命盘',
      es: 'Carta Natal',
      pt: 'Mapa Natal',
      hi: 'जन्म कुंडली',
      ja: '出生図',
    },
    description: {
      en: 'Discover your complete astrological blueprint',
      zh: '发现你完整的星象蓝图',
      es: 'Descubre tu plano astrológico completo',
      pt: 'Descubra seu mapa astrológico completo',
      hi: 'अपना पूर्ण ज्योतिषीय खाका खोजें',
      ja: 'あなたの完全な占星術の設計図を発見',
    },
    href: '/astrology/natal-chart',
  },
  {
    id: 'compatibility',
    icon: '💕',
    title: {
      en: 'Compatibility',
      zh: '配对分析',
      es: 'Compatibilidad',
      pt: 'Compatibilidade',
      hi: 'संगतता',
      ja: '相性',
    },
    description: {
      en: 'Explore relationship compatibility through astrology',
      zh: '通过占星术探索关系兼容性',
      es: 'Explora la compatibilidad de relaciones a través de la astrología',
      pt: 'Explore a compatibilidade de relacionamentos através da astrologia',
      hi: 'ज्योतिष के माध्यम से रिश्ते की संगतता का अन्वेषण करें',
      ja: '占星術を通して関係の相性を探求',
    },
    href: '/astrology/compatibility',
  },
];

export default function AstrologyPage({ params: { locale } }: AstrologyPageProps) {
  const t = useTranslations();

  const getHref = (href: string) => {
    return locale === 'en' ? href : `/${locale}${href}`;
  };

  const getLocalizedContent = (content: any) => {
    return content[locale] || content.en || '';
  };

  return (
    <PageLayout locale={locale}>
      <div className="min-h-screen bg-gradient-to-br from-cosmic-50 via-mystical-50 to-cosmic-100 dark:from-cosmic-950 dark:via-mystical-950 dark:to-cosmic-900">
        <div className="container mx-auto px-4 py-16">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <div className="text-6xl mb-6">⭐</div>
            <h1 className="text-responsive-xl font-serif font-bold text-gradient mb-6">
              {t('astrology.title')}
            </h1>
            <p className="text-responsive-md text-muted-foreground max-w-3xl mx-auto mb-8">
              {t('astrology.description')}
            </p>
            <Button variant="cosmic" size="lg">
              Explore Your Stars
            </Button>
          </div>

          {/* Astrology Services */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-12">Astrological Services</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {astrologyServices.map((service) => (
                <Card key={service.id} variant="cosmic" hover className="text-center">
                  <CardHeader>
                    <div className="text-4xl mb-4">{service.icon}</div>
                    <CardTitle className="text-xl">
                      {getLocalizedContent(service.title)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="mb-6">
                      {getLocalizedContent(service.description)}
                    </CardDescription>
                    <Link href={getHref(service.href)}>
                      <Button variant="outline" className="w-full">
                        Get Started
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Zodiac Signs */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-12">Zodiac Signs</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
              {zodiacSigns.map((zodiac) => (
                <Card key={zodiac.sign} variant="mystical" hover className="text-center">
                  <CardHeader className="pb-2">
                    <div className="text-3xl mb-2">{zodiac.emoji}</div>
                    <CardTitle className="text-lg">
                      {t(`astrology.signs.${zodiac.sign}`)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="text-xs text-muted-foreground mb-1">
                      {zodiac.dates}
                    </div>
                    <div className="text-xs font-medium text-primary">
                      {zodiac.element}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Astrological Elements */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-12">Astrological Elements</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card variant="cosmic" hover className="text-center">
                <CardHeader>
                  <div className="text-4xl mb-2">🔥</div>
                  <CardTitle>Fire Signs</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Aries, Leo, Sagittarius - Passionate, energetic, and dynamic
                  </CardDescription>
                </CardContent>
              </Card>

              <Card variant="cosmic" hover className="text-center">
                <CardHeader>
                  <div className="text-4xl mb-2">🌍</div>
                  <CardTitle>Earth Signs</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Taurus, Virgo, Capricorn - Practical, stable, and grounded
                  </CardDescription>
                </CardContent>
              </Card>

              <Card variant="cosmic" hover className="text-center">
                <CardHeader>
                  <div className="text-4xl mb-2">💨</div>
                  <CardTitle>Air Signs</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Gemini, Libra, Aquarius - Intellectual, social, and communicative
                  </CardDescription>
                </CardContent>
              </Card>

              <Card variant="cosmic" hover className="text-center">
                <CardHeader>
                  <div className="text-4xl mb-2">🌊</div>
                  <CardTitle>Water Signs</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Cancer, Scorpio, Pisces - Emotional, intuitive, and sensitive
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Today's Cosmic Weather */}
          <div className="mb-16">
            <Card variant="glass" className="max-w-4xl mx-auto">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl">Today's Cosmic Weather</CardTitle>
                <CardDescription>Current planetary influences and energies</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-2xl mb-2">🌙</div>
                    <h4 className="font-semibold mb-1">Moon Phase</h4>
                    <p className="text-sm text-muted-foreground">Waxing Crescent in Gemini</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl mb-2">☿️</div>
                    <h4 className="font-semibold mb-1">Mercury</h4>
                    <p className="text-sm text-muted-foreground">Direct in Capricorn</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl mb-2">♀️</div>
                    <h4 className="font-semibold mb-1">Venus</h4>
                    <p className="text-sm text-muted-foreground">In Aquarius</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Call to Action */}
          <div className="text-center">
            <Card variant="mystical" className="max-w-2xl mx-auto">
              <CardHeader>
                <CardTitle className="text-2xl">Unlock Your Cosmic Potential</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="mb-6">
                  Discover what the stars have in store for you with personalized astrological insights
                </CardDescription>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href={getHref('/astrology/daily')}>
                    <Button variant="cosmic" size="lg">
                      Daily Horoscope
                    </Button>
                  </Link>
                  <Link href={getHref('/astrology/natal-chart')}>
                    <Button variant="outline" size="lg">
                      Birth Chart
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
