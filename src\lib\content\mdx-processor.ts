import { compile } from '@mdx-js/mdx';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import rehypeHighlight from 'rehype-highlight';
import rehypeSlug from 'rehype-slug';
import rehypeAutolinkHeadings from 'rehype-autolink-headings';
import { visit } from 'unist-util-visit';
import type { Locale } from '@/types';

// MDX内容接口
export interface MDXContent {
  content: string;
  frontmatter: Record<string, any>;
  readingTime: number;
  wordCount: number;
  headings: Array<{
    level: number;
    text: string;
    slug: string;
  }>;
}

// 阅读时间计算
function calculateReadingTime(text: string): number {
  const wordsPerMinute = 200;
  const words = text.trim().split(/\s+/).length;
  return Math.ceil(words / wordsPerMinute);
}

// 提取标题
function extractHeadings() {
  return (tree: any) => {
    const headings: Array<{ level: number; text: string; slug: string }> = [];
    
    visit(tree, 'element', (node) => {
      if (node.tagName && /^h[1-6]$/.test(node.tagName)) {
        const level = parseInt(node.tagName.charAt(1));
        const text = extractTextFromNode(node);
        const slug = text.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
        
        headings.push({ level, text, slug });
      }
    });
    
    tree.headings = headings;
  };
}

// 从节点提取文本
function extractTextFromNode(node: any): string {
  if (node.type === 'text') {
    return node.value;
  }
  
  if (node.children) {
    return node.children.map(extractTextFromNode).join('');
  }
  
  return '';
}

// 自定义组件映射
const customComponents = {
  // 警告框组件
  Alert: ({ type = 'info', children }: { type?: 'info' | 'warning' | 'error' | 'success'; children: React.ReactNode }) => `
    <div class="alert alert-${type} p-4 mb-4 rounded-lg border-l-4 ${
      type === 'info' ? 'bg-blue-50 border-blue-400 text-blue-800' :
      type === 'warning' ? 'bg-yellow-50 border-yellow-400 text-yellow-800' :
      type === 'error' ? 'bg-red-50 border-red-400 text-red-800' :
      'bg-green-50 border-green-400 text-green-800'
    }">
      ${children}
    </div>
  `,
  
  // 引用框组件
  Quote: ({ author, children }: { author?: string; children: React.ReactNode }) => `
    <blockquote class="border-l-4 border-mystical-500 pl-4 py-2 my-4 italic bg-muted/30 rounded-r-lg">
      <div class="text-lg">${children}</div>
      ${author ? `<cite class="text-sm text-muted-foreground mt-2 block">— ${author}</cite>` : ''}
    </blockquote>
  `,
  
  // 塔罗牌组件
  TarotCard: ({ name, meaning, reversed = false }: { name: string; meaning: string; reversed?: boolean }) => `
    <div class="tarot-card p-4 border rounded-lg bg-gradient-to-br from-mystical-50 to-cosmic-50 my-4">
      <div class="flex items-center mb-2">
        <span class="text-2xl mr-2">🃏</span>
        <h4 class="font-semibold text-lg">${name}${reversed ? ' (Reversed)' : ''}</h4>
      </div>
      <p class="text-muted-foreground">${meaning}</p>
    </div>
  `,
  
  // 星座信息组件
  ZodiacSign: ({ sign, dates, element }: { sign: string; dates: string; element: string }) => `
    <div class="zodiac-sign p-4 border rounded-lg bg-gradient-to-br from-cosmic-50 to-mystical-50 my-4">
      <div class="flex items-center justify-between mb-2">
        <h4 class="font-semibold text-lg">${sign}</h4>
        <span class="text-sm text-muted-foreground">${element}</span>
      </div>
      <p class="text-sm text-muted-foreground">${dates}</p>
    </div>
  `,
  
  // 数字命理组件
  NumerologyNumber: ({ number, meaning }: { number: number; meaning: string }) => `
    <div class="numerology-number p-4 border rounded-lg bg-gradient-to-br from-mystical-50 to-cosmic-50 my-4">
      <div class="flex items-center mb-2">
        <span class="text-3xl font-bold text-mystical-600 mr-3">${number}</span>
        <div>
          <h4 class="font-semibold">Number ${number}</h4>
          <p class="text-sm text-muted-foreground">${meaning}</p>
        </div>
      </div>
    </div>
  `,
};

// MDX处理器类
export class MDXProcessor {
  private static instance: MDXProcessor;
  
  static getInstance(): MDXProcessor {
    if (!MDXProcessor.instance) {
      MDXProcessor.instance = new MDXProcessor();
    }
    return MDXProcessor.instance;
  }

  // 处理MDX内容
  async processMDX(
    source: string,
    locale: Locale = 'en',
    options: {
      includeFrontmatter?: boolean;
      includeHeadings?: boolean;
      customComponents?: Record<string, any>;
    } = {}
  ): Promise<MDXContent> {
    const {
      includeFrontmatter = true,
      includeHeadings = true,
      customComponents: userComponents = {},
    } = options;

    // 提取frontmatter
    let frontmatter = {};
    let content = source;
    
    if (includeFrontmatter && source.startsWith('---')) {
      const frontmatterMatch = source.match(/^---\n([\s\S]*?)\n---\n([\s\S]*)$/);
      if (frontmatterMatch) {
        try {
          frontmatter = this.parseFrontmatter(frontmatterMatch[1]);
          content = frontmatterMatch[2];
        } catch (error) {
          console.warn('Failed to parse frontmatter:', error);
        }
      }
    }

    // 计算阅读时间和字数
    const plainText = content.replace(/[#*`_~\[\]()]/g, '').replace(/\n+/g, ' ');
    const readingTime = calculateReadingTime(plainText);
    const wordCount = plainText.trim().split(/\s+/).length;

    try {
      // 编译MDX
      const compiledMDX = await compile(content, {
        remarkPlugins: [
          remarkGfm,
          remarkMath,
        ],
        rehypePlugins: [
          rehypeSlug,
          [rehypeAutolinkHeadings, { behavior: 'wrap' }],
          rehypeHighlight,
          rehypeKatex,
          ...(includeHeadings ? [extractHeadings] : []),
        ],
        development: process.env.NODE_ENV === 'development',
      });

      // 合并自定义组件
      const components = { ...customComponents, ...userComponents };

      return {
        content: String(compiledMDX),
        frontmatter,
        readingTime,
        wordCount,
        headings: includeHeadings ? this.extractHeadingsFromContent(content) : [],
      };
    } catch (error) {
      console.error('MDX compilation error:', error);
      throw new Error(`Failed to compile MDX: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // 解析frontmatter
  private parseFrontmatter(frontmatterText: string): Record<string, any> {
    const frontmatter: Record<string, any> = {};
    
    frontmatterText.split('\n').forEach(line => {
      const colonIndex = line.indexOf(':');
      if (colonIndex > 0) {
        const key = line.substring(0, colonIndex).trim();
        const value = line.substring(colonIndex + 1).trim();
        
        // 尝试解析值
        try {
          if (value.startsWith('[') && value.endsWith(']')) {
            frontmatter[key] = JSON.parse(value);
          } else if (value === 'true' || value === 'false') {
            frontmatter[key] = value === 'true';
          } else if (!isNaN(Number(value))) {
            frontmatter[key] = Number(value);
          } else {
            frontmatter[key] = value.replace(/^["']|["']$/g, '');
          }
        } catch {
          frontmatter[key] = value.replace(/^["']|["']$/g, '');
        }
      }
    });
    
    return frontmatter;
  }

  // 从内容中提取标题
  private extractHeadingsFromContent(content: string): Array<{ level: number; text: string; slug: string }> {
    const headings: Array<{ level: number; text: string; slug: string }> = [];
    const lines = content.split('\n');
    
    lines.forEach(line => {
      const match = line.match(/^(#{1,6})\s+(.+)$/);
      if (match) {
        const level = match[1].length;
        const text = match[2].trim();
        const slug = text.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
        
        headings.push({ level, text, slug });
      }
    });
    
    return headings;
  }

  // 生成目录
  generateTableOfContents(headings: Array<{ level: number; text: string; slug: string }>): string {
    if (headings.length === 0) return '';
    
    let toc = '<nav class="table-of-contents"><ul>';
    let currentLevel = 0;
    
    headings.forEach(heading => {
      if (heading.level > currentLevel) {
        toc += '<ul>'.repeat(heading.level - currentLevel);
      } else if (heading.level < currentLevel) {
        toc += '</ul>'.repeat(currentLevel - heading.level);
      }
      
      toc += `<li><a href="#${heading.slug}">${heading.text}</a></li>`;
      currentLevel = heading.level;
    });
    
    toc += '</ul>'.repeat(currentLevel) + '</nav>';
    return toc;
  }

  // 验证MDX语法
  async validateMDX(source: string): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];
    
    try {
      await compile(source, {
        remarkPlugins: [remarkGfm],
        development: true,
      });
      return { valid: true, errors: [] };
    } catch (error) {
      errors.push(error instanceof Error ? error.message : 'Unknown compilation error');
      return { valid: false, errors };
    }
  }

  // 预处理内容（清理和优化）
  preprocessContent(content: string, locale: Locale): string {
    // 移除多余的空行
    content = content.replace(/\n{3,}/g, '\n\n');
    
    // 修复常见的Markdown问题
    content = content.replace(/([.!?])\s*\n\s*([A-Z])/g, '$1\n\n$2');
    
    // 添加语言特定的处理
    if (locale === 'zh') {
      // 中文标点符号优化
      content = content.replace(/，\s+/g, '，');
      content = content.replace(/。\s+/g, '。');
    }
    
    return content;
  }

  // 后处理HTML（添加样式类等）
  postprocessHTML(html: string, locale: Locale): string {
    // 添加响应式图片类
    html = html.replace(/<img([^>]*)>/g, '<img$1 class="responsive-image rounded-lg shadow-md">');
    
    // 添加表格样式
    html = html.replace(/<table>/g, '<table class="table-auto w-full border-collapse border border-border">');
    html = html.replace(/<th>/g, '<th class="border border-border px-4 py-2 bg-muted font-semibold">');
    html = html.replace(/<td>/g, '<td class="border border-border px-4 py-2">');
    
    // 添加代码块样式
    html = html.replace(/<pre><code/g, '<pre class="bg-muted p-4 rounded-lg overflow-x-auto"><code');
    
    // 添加链接样式
    html = html.replace(/<a href="([^"]*)"([^>]*)>/g, '<a href="$1"$2 class="text-primary hover:text-primary/80 underline">');
    
    return html;
  }
}

// 导出单例实例
export const mdxProcessor = MDXProcessor.getInstance();

export default mdxProcessor;
