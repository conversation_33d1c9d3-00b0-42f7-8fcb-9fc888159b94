import Script from 'next/script';
import type { Locale } from '@/types';

interface StructuredDataProps {
  data: Record<string, any>;
}

export function StructuredData({ data }: StructuredDataProps) {
  return (
    <Script
      id="structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
    />
  );
}

// 预定义的结构化数据模板
export const structuredDataTemplates = {
  // 网站基础信息
  website: (locale: Locale) => ({
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: locale === 'zh' ? '神秘洞察' : 
          locale === 'es' ? 'Perspectivas Místicas' :
          locale === 'pt' ? 'Percepções Místicas' :
          locale === 'hi' ? 'रहस्यमय अंतर्दृष्टि' :
          locale === 'ja' ? 'ミスティカル・インサイト' :
          'Mystical Insights',
    description: locale === 'zh' ? '专业的塔罗占卜、占星洞察和灵性指导服务' :
                locale === 'es' ? 'Servicios profesionales de tarot, astrología y guía espiritual' :
                locale === 'pt' ? 'Serviços profissionais de tarô, astrologia e orientação espiritual' :
                locale === 'hi' ? 'पेशेवर टैरो, ज्योतिष और आध्यात्मिक मार्गदर्शन सेवाएं' :
                locale === 'ja' ? 'プロのタロット、占星術、スピリチュアルガイダンスサービス' :
                'Professional tarot, astrology, and spiritual guidance services',
    url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
    potentialAction: {
      '@type': 'SearchAction',
      target: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string',
    },
    sameAs: [
      'https://facebook.com/mysticalinsights',
      'https://twitter.com/mysticalinsights',
      'https://instagram.com/mysticalinsights',
    ],
  }),

  // 组织信息
  organization: (locale: Locale) => ({
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: locale === 'zh' ? '神秘洞察' : 
          locale === 'es' ? 'Perspectivas Místicas' :
          locale === 'pt' ? 'Percepções Místicas' :
          locale === 'hi' ? 'रहस्यमय अंतर्दृष्टि' :
          locale === 'ja' ? 'ミスティカル・インサイト' :
          'Mystical Insights',
    url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
    logo: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/logo.png`,
    description: locale === 'zh' ? '提供专业塔罗占卜、占星分析和灵性指导的权威平台' :
                locale === 'es' ? 'Plataforma autorizada que ofrece tarot profesional, análisis astrológico y guía espiritual' :
                locale === 'pt' ? 'Plataforma autorizada oferecendo tarô profissional, análise astrológica e orientação espiritual' :
                locale === 'hi' ? 'पेशेवर टैरो, ज्योतिषीय विश्लेषण और आध्यात्मिक मार्गदर्शन प्रदान करने वाला प्राधिकृत मंच' :
                locale === 'ja' ? 'プロのタロット、占星術分析、スピリチュアルガイダンスを提供する権威あるプラットフォーム' :
                'Authoritative platform providing professional tarot, astrological analysis, and spiritual guidance',
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '******-MYSTICAL',
      contactType: 'customer service',
      availableLanguage: ['English', 'Chinese', 'Spanish', 'Portuguese', 'Hindi', 'Japanese'],
    },
    sameAs: [
      'https://facebook.com/mysticalinsights',
      'https://twitter.com/mysticalinsights',
      'https://instagram.com/mysticalinsights',
    ],
  }),

  // 文章结构化数据
  article: (article: {
    title: string;
    description: string;
    author: string;
    publishedAt: string;
    updatedAt: string;
    image?: string;
    url: string;
    category: string;
  }) => ({
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: article.title,
    description: article.description,
    image: article.image,
    author: {
      '@type': 'Person',
      name: article.author,
    },
    publisher: {
      '@type': 'Organization',
      name: 'Mystical Insights',
      logo: {
        '@type': 'ImageObject',
        url: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/logo.png`,
      },
    },
    datePublished: article.publishedAt,
    dateModified: article.updatedAt,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': article.url,
    },
    articleSection: article.category,
  }),

  // 商品结构化数据
  product: (product: {
    name: string;
    description: string;
    price: number;
    currency: string;
    image: string;
    url: string;
    brand: string;
    sku: string;
    availability: 'InStock' | 'OutOfStock';
  }) => ({
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: product.name,
    description: product.description,
    image: product.image,
    brand: {
      '@type': 'Brand',
      name: product.brand,
    },
    sku: product.sku,
    offers: {
      '@type': 'Offer',
      price: product.price,
      priceCurrency: product.currency,
      availability: `https://schema.org/${product.availability}`,
      url: product.url,
    },
  }),

  // 服务结构化数据
  service: (service: {
    name: string;
    description: string;
    provider: string;
    serviceType: string;
    url: string;
  }) => ({
    '@context': 'https://schema.org',
    '@type': 'Service',
    name: service.name,
    description: service.description,
    provider: {
      '@type': 'Organization',
      name: service.provider,
    },
    serviceType: service.serviceType,
    url: service.url,
  }),

  // FAQ结构化数据
  faq: (faqs: Array<{ question: string; answer: string }>) => ({
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  }),

  // 面包屑导航
  breadcrumb: (items: Array<{ name: string; url: string }>) => ({
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  }),

  // 本地商业信息（如果有实体店）
  localBusiness: (locale: Locale) => ({
    '@context': 'https://schema.org',
    '@type': 'LocalBusiness',
    name: locale === 'zh' ? '神秘洞察' : 
          locale === 'es' ? 'Perspectivas Místicas' :
          locale === 'pt' ? 'Percepções Místicas' :
          locale === 'hi' ? 'रहस्यमय अंतर्दृष्टि' :
          locale === 'ja' ? 'ミスティカル・インサイト' :
          'Mystical Insights',
    description: locale === 'zh' ? '专业的塔罗占卜和灵性指导服务' :
                locale === 'es' ? 'Servicios profesionales de tarot y guía espiritual' :
                locale === 'pt' ? 'Serviços profissionais de tarô e orientação espiritual' :
                locale === 'hi' ? 'पेशेवर टैरो और आध्यात्मिक मार्गदर्शन सेवाएं' :
                locale === 'ja' ? 'プロのタロットとスピリチュアルガイダンスサービス' :
                'Professional tarot and spiritual guidance services',
    url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
    telephone: '******-MYSTICAL',
    address: {
      '@type': 'PostalAddress',
      streetAddress: '123 Mystical Street',
      addressLocality: 'Spiritual City',
      addressRegion: 'Cosmic State',
      postalCode: '12345',
      addressCountry: 'US',
    },
    openingHours: 'Mo-Su 00:00-23:59',
    priceRange: '$$',
  }),
};

export default StructuredData;
