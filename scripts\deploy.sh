#!/bin/bash

# 玄学多语言网站部署脚本
# 支持多环境部署：development, staging, production

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必需的工具
check_dependencies() {
    log_info "检查部署依赖..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    if ! command -v git &> /dev/null; then
        log_error "Git 未安装"
        exit 1
    fi
    
    log_success "所有依赖检查通过"
}

# 环境变量检查
check_environment() {
    local env=$1
    log_info "检查 $env 环境配置..."
    
    if [ ! -f ".env.$env" ]; then
        log_error "环境配置文件 .env.$env 不存在"
        exit 1
    fi
    
    # 检查关键环境变量
    source ".env.$env"
    
    if [ -z "$DATABASE_URL" ]; then
        log_error "DATABASE_URL 未配置"
        exit 1
    fi
    
    if [ -z "$NEXT_PUBLIC_SITE_URL" ]; then
        log_error "NEXT_PUBLIC_SITE_URL 未配置"
        exit 1
    fi
    
    log_success "环境配置检查通过"
}

# 代码质量检查
run_quality_checks() {
    log_info "运行代码质量检查..."
    
    # TypeScript 类型检查
    log_info "运行 TypeScript 类型检查..."
    npm run type-check
    
    # ESLint 检查
    log_info "运行 ESLint 检查..."
    npm run lint
    
    # 运行测试
    if [ -f "package.json" ] && grep -q '"test"' package.json; then
        log_info "运行测试..."
        npm run test
    fi
    
    log_success "代码质量检查通过"
}

# 构建项目
build_project() {
    local env=$1
    log_info "构建 $env 环境项目..."
    
    # 复制环境配置
    cp ".env.$env" ".env.local"
    
    # 安装依赖
    log_info "安装依赖..."
    npm ci
    
    # 生成 Prisma 客户端
    log_info "生成 Prisma 客户端..."
    npx prisma generate
    
    # 构建项目
    log_info "构建 Next.js 项目..."
    npm run build
    
    log_success "项目构建完成"
}

# 数据库迁移
run_database_migration() {
    local env=$1
    log_info "运行数据库迁移..."
    
    if [ "$env" = "production" ]; then
        log_warning "生产环境数据库迁移需要额外确认"
        read -p "确认运行生产环境数据库迁移? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "跳过数据库迁移"
            return
        fi
    fi
    
    npx prisma migrate deploy
    
    log_success "数据库迁移完成"
}

# 部署到 Vercel
deploy_to_vercel() {
    local env=$1
    log_info "部署到 Vercel ($env)..."
    
    if ! command -v vercel &> /dev/null; then
        log_error "Vercel CLI 未安装，请运行: npm i -g vercel"
        exit 1
    fi
    
    case $env in
        "development")
            vercel --dev
            ;;
        "staging")
            vercel --target preview
            ;;
        "production")
            vercel --prod
            ;;
        *)
            log_error "未知环境: $env"
            exit 1
            ;;
    esac
    
    log_success "Vercel 部署完成"
}

# 部署后验证
post_deploy_verification() {
    local url=$1
    log_info "部署后验证..."
    
    # 等待部署完成
    sleep 10
    
    # 检查网站是否可访问
    if command -v curl &> /dev/null; then
        if curl -f -s "$url/api/health" > /dev/null; then
            log_success "网站健康检查通过"
        else
            log_error "网站健康检查失败"
            exit 1
        fi
    fi
    
    # 检查关键页面
    local pages=("/" "/blog" "/tarot" "/astrology" "/products" "/tests")
    for page in "${pages[@]}"; do
        if curl -f -s "$url$page" > /dev/null; then
            log_success "页面 $page 可访问"
        else
            log_warning "页面 $page 可能有问题"
        fi
    done
    
    log_success "部署验证完成"
}

# 发送部署通知
send_notification() {
    local env=$1
    local url=$2
    local status=$3
    
    log_info "发送部署通知..."
    
    # 这里可以集成 Slack、Discord、邮件等通知服务
    # 示例：发送到 Slack
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🚀 Mystical Insights 部署 $status\\n环境: $env\\nURL: $url\"}" \
            "$SLACK_WEBHOOK_URL"
    fi
    
    log_success "部署通知已发送"
}

# 主函数
main() {
    local env=${1:-"development"}
    local skip_checks=${2:-false}
    
    log_info "开始部署玄学多语言网站到 $env 环境"
    
    # 检查依赖
    check_dependencies
    
    # 检查环境配置
    check_environment "$env"
    
    # 代码质量检查（可选跳过）
    if [ "$skip_checks" != "true" ]; then
        run_quality_checks
    fi
    
    # 构建项目
    build_project "$env"
    
    # 数据库迁移
    run_database_migration "$env"
    
    # 部署到 Vercel
    deploy_to_vercel "$env"
    
    # 获取部署URL
    source ".env.$env"
    local deploy_url="$NEXT_PUBLIC_SITE_URL"
    
    # 部署后验证
    post_deploy_verification "$deploy_url"
    
    # 发送通知
    send_notification "$env" "$deploy_url" "成功"
    
    log_success "🎉 部署完成！"
    log_info "网站地址: $deploy_url"
    log_info "管理后台: $deploy_url/admin"
}

# 显示帮助信息
show_help() {
    echo "玄学多语言网站部署脚本"
    echo ""
    echo "用法: $0 [环境] [选项]"
    echo ""
    echo "环境:"
    echo "  development  开发环境 (默认)"
    echo "  staging      预发布环境"
    echo "  production   生产环境"
    echo ""
    echo "选项:"
    echo "  --skip-checks  跳过代码质量检查"
    echo "  --help         显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 production"
    echo "  $0 staging --skip-checks"
}

# 解析命令行参数
case $1 in
    --help|-h)
        show_help
        exit 0
        ;;
    *)
        if [ "$2" = "--skip-checks" ]; then
            main "$1" true
        else
            main "$1" false
        fi
        ;;
esac
