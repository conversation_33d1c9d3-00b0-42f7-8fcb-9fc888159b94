import { useTranslations } from 'next-intl';
import { getTranslations } from 'next-intl/server';
import type { Metadata } from 'next';
import type { Locale } from '@/types';
import { PageLayout } from '@/components/layout/page-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { generateSEOMetadata, seoTemplates } from '@/components/seo/seo-head';
import Link from 'next/link';

interface BlogPageProps {
  params: { locale: Locale };
  searchParams: { page?: string; category?: string };
}

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: Locale };
}): Promise<Metadata> {
  const t = await getTranslations({ locale, namespace: 'seo.blog' });

  const seoData = {
    title: t('title'),
    description: t('description'),
    keywords: ['blog', 'articles', 'mystical', 'tarot', 'astrology', 'spirituality'],
    ogType: 'website' as const,
    twitterCard: 'summary_large_image' as const,
  };

  return generateSEOMetadata({
    seo: seoData,
    locale,
    path: '/blog',
  });
}

// 模拟博客文章数据
const mockArticles = [
  {
    id: '1',
    slug: 'understanding-tarot-basics',
    title: {
      en: 'Understanding Tarot Basics: A Beginner\'s Guide',
      zh: '塔罗基础知识：初学者指南',
      es: 'Entendiendo los Fundamentos del Tarot: Una Guía para Principiantes',
      pt: 'Entendendo os Fundamentos do Tarô: Um Guia para Iniciantes',
      hi: 'टैरो की मूल बातें समझना: एक शुरुआती गाइड',
      ja: 'タロットの基本を理解する：初心者向けガイド',
    },
    excerpt: {
      en: 'Learn the fundamental concepts of tarot reading and discover how to interpret the cards.',
      zh: '学习塔罗占卜的基本概念，发现如何解读卡牌。',
      es: 'Aprende los conceptos fundamentales de la lectura del tarot y descubre cómo interpretar las cartas.',
      pt: 'Aprenda os conceitos fundamentais da leitura de tarô e descubra como interpretar as cartas.',
      hi: 'टैरो रीडिंग की मूलभूत अवधारणाओं को सीखें और कार्डों की व्याख्या करना खोजें।',
      ja: 'タロットリーディングの基本概念を学び、カードの解釈方法を発見しましょう。',
    },
    category: 'Tarot',
    author: 'Mystical Team',
    publishedAt: '2024-01-15',
    readingTime: 5,
    featured: true,
    coverImage: '/images/blog/tarot-basics.jpg',
  },
  {
    id: '2',
    slug: 'daily-astrology-guide',
    title: {
      en: 'Your Daily Astrology Guide: Reading the Stars',
      zh: '每日占星指南：解读星象',
      es: 'Tu Guía Diaria de Astrología: Leyendo las Estrellas',
      pt: 'Seu Guia Diário de Astrologia: Lendo as Estrelas',
      hi: 'आपकी दैनिक ज्योतिष गाइड: सितारों को पढ़ना',
      ja: 'あなたの毎日の占星術ガイド：星を読む',
    },
    excerpt: {
      en: 'Discover how to read daily astrological influences and apply them to your life.',
      zh: '发现如何解读每日星象影响并将其应用到你的生活中。',
      es: 'Descubre cómo leer las influencias astrológicas diarias y aplicarlas a tu vida.',
      pt: 'Descubra como ler as influências astrológicas diárias e aplicá-las à sua vida.',
      hi: 'दैनिक ज्योतिषीय प्रभावों को पढ़ना और उन्हें अपने जीवन में लागू करना सीखें।',
      ja: '毎日の占星術の影響を読み、それをあなたの人生に適用する方法を発見してください。',
    },
    category: 'Astrology',
    author: 'Star Reader',
    publishedAt: '2024-01-12',
    readingTime: 7,
    featured: false,
    coverImage: '/images/blog/astrology-guide.jpg',
  },
  {
    id: '3',
    slug: 'numerology-life-path',
    title: {
      en: 'Finding Your Life Path Through Numerology',
      zh: '通过数字命理找到你的人生道路',
      es: 'Encontrando tu Camino de Vida a través de la Numerología',
      pt: 'Encontrando seu Caminho de Vida através da Numerologia',
      hi: 'अंकशास्त्र के माध्यम से अपना जीवन पथ खोजना',
      ja: '数秘術を通してあなたの人生の道を見つける',
    },
    excerpt: {
      en: 'Explore the power of numbers and learn how to calculate your life path number.',
      zh: '探索数字的力量，学习如何计算你的生命数字。',
      es: 'Explora el poder de los números y aprende cómo calcular tu número de camino de vida.',
      pt: 'Explore o poder dos números e aprenda como calcular seu número do caminho da vida.',
      hi: 'संख्याओं की शक्ति का अन्वेषण करें और अपना जीवन पथ संख्या की गणना करना सीखें।',
      ja: '数字の力を探求し、あなたのライフパス数の計算方法を学びましょう。',
    },
    category: 'Numerology',
    author: 'Number Sage',
    publishedAt: '2024-01-10',
    readingTime: 6,
    featured: true,
    coverImage: '/images/blog/numerology-path.jpg',
  },
];

export default function BlogPage({ params: { locale }, searchParams }: BlogPageProps) {
  const t = useTranslations();
  const currentPage = parseInt(searchParams.page || '1');
  const selectedCategory = searchParams.category;

  // 过滤文章
  const filteredArticles = selectedCategory
    ? mockArticles.filter(article => article.category.toLowerCase() === selectedCategory.toLowerCase())
    : mockArticles;

  const categories = ['All', 'Tarot', 'Astrology', 'Numerology', 'Spirituality'];

  const getHref = (href: string) => {
    return locale === 'en' ? href : `/${locale}${href}`;
  };

  const getLocalizedContent = (content: any) => {
    return content[locale] || content.en || '';
  };

  return (
    <PageLayout locale={locale}>
      <div className="min-h-screen bg-gradient-to-br from-background via-muted/20 to-background">
        <div className="container mx-auto px-4 py-16">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-responsive-xl font-serif font-bold text-gradient mb-6">
              {t('blog.title')}
            </h1>
            <p className="text-responsive-md text-muted-foreground max-w-3xl mx-auto">
              Explore our collection of mystical insights, spiritual guidance, and ancient wisdom.
            </p>
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-2 mb-12">
            {categories.map((category) => (
              <Link
                key={category}
                href={getHref(`/blog${category !== 'All' ? `?category=${category.toLowerCase()}` : ''}`)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                  (category === 'All' && !selectedCategory) ||
                  category.toLowerCase() === selectedCategory
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                }`}
              >
                {category}
              </Link>
            ))}
          </div>

          {/* Featured Articles */}
          {!selectedCategory && (
            <div className="mb-16">
              <h2 className="text-2xl font-bold mb-8">{t('blog.featured_posts')}</h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {mockArticles
                  .filter(article => article.featured)
                  .map((article) => (
                    <Card key={article.id} variant="mystical" hover className="overflow-hidden">
                      <div className="aspect-video bg-gradient-to-br from-mystical-100 to-cosmic-100 flex items-center justify-center">
                        <span className="text-4xl">🔮</span>
                      </div>
                      <CardHeader>
                        <div className="flex items-center justify-between text-sm text-muted-foreground mb-2">
                          <span>{article.category}</span>
                          <span>{article.readingTime} {t('blog.minutes')}</span>
                        </div>
                        <CardTitle className="text-xl">
                          {getLocalizedContent(article.title)}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <CardDescription className="mb-4">
                          {getLocalizedContent(article.excerpt)}
                        </CardDescription>
                        <div className="flex items-center justify-between">
                          <div className="text-sm text-muted-foreground">
                            <span>{t('blog.author')}: {article.author}</span>
                            <span className="mx-2">•</span>
                            <span>{article.publishedAt}</span>
                          </div>
                          <Link href={getHref(`/blog/${article.slug}`)}>
                            <Button variant="ghost" size="sm">
                              {t('blog.read_more')}
                            </Button>
                          </Link>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
              </div>
            </div>
          )}

          {/* All Articles */}
          <div>
            <h2 className="text-2xl font-bold mb-8">
              {selectedCategory ? `${selectedCategory} Articles` : t('blog.latest_posts')}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredArticles.map((article) => (
                <Card key={article.id} variant="mystical" hover className="overflow-hidden">
                  <div className="aspect-video bg-gradient-to-br from-mystical-100 to-cosmic-100 flex items-center justify-center">
                    <span className="text-3xl">
                      {article.category === 'Tarot' ? '🔮' :
                       article.category === 'Astrology' ? '⭐' :
                       article.category === 'Numerology' ? '🔢' : '🌙'}
                    </span>
                  </div>
                  <CardHeader>
                    <div className="flex items-center justify-between text-sm text-muted-foreground mb-2">
                      <span>{article.category}</span>
                      <span>{article.readingTime} {t('blog.minutes')}</span>
                    </div>
                    <CardTitle className="text-lg">
                      {getLocalizedContent(article.title)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="mb-4">
                      {getLocalizedContent(article.excerpt)}
                    </CardDescription>
                    <div className="flex items-center justify-between">
                      <div className="text-xs text-muted-foreground">
                        {article.publishedAt}
                      </div>
                      <Link href={getHref(`/blog/${article.slug}`)}>
                        <Button variant="ghost" size="sm">
                          {t('blog.read_more')}
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Pagination */}
          <div className="flex justify-center mt-12">
            <div className="flex items-center space-x-2">
              <Button variant="outline" disabled={currentPage === 1}>
                {t('common.previous')}
              </Button>
              <span className="px-4 py-2 text-sm">
                Page {currentPage}
              </span>
              <Button variant="outline">
                {t('common.next')}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
