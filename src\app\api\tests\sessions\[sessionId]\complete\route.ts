import { NextRequest, NextResponse } from 'next/server';
import { TestFlowManager } from '@/lib/tests/test-flow-manager';

// POST /api/tests/sessions/[sessionId]/complete - 完成测试
export async function POST(
  request: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    const { sessionId } = params;

    const testFlowManager = new TestFlowManager();
    const result = await testFlowManager.completeTest(sessionId);

    return NextResponse.json({
      success: true,
      result,
      message: 'Test completed successfully',
    });
  } catch (error) {
    console.error('Error completing test:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to complete test',
      },
      { status: 500 }
    );
  }
}

// GET /api/tests/sessions/[sessionId]/complete - 获取测试结果
export async function GET(
  request: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    const { sessionId } = params;

    const testFlowManager = new TestFlowManager();
    const result = await testFlowManager.getTestResult(sessionId);

    if (!result) {
      return NextResponse.json(
        { success: false, error: 'Test result not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      result,
    });
  } catch (error) {
    console.error('Error getting test result:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get test result',
      },
      { status: 500 }
    );
  }
}
