import { NextRequest, NextResponse } from 'next/server';
import { ContentManager } from '@/lib/content/content-manager';
import type { Locale } from '@/types';

// GET /api/content/articles - 获取文章列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const locale = searchParams.get('locale') as Locale || 'en';
    const category = searchParams.get('category') || undefined;
    const status = searchParams.get('status') as any || 'PUBLISHED';
    const featured = searchParams.get('featured') ? searchParams.get('featured') === 'true' : undefined;
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');

    const result = await ContentManager.getArticles({
      locale,
      category,
      status,
      featured,
      limit,
      offset,
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Articles API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch articles' },
      { status: 500 }
    );
  }
}

// POST /api/content/articles - 创建文章
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 验证必需字段
    const requiredFields = ['slug', 'title', 'content', 'authorId', 'categoryId'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // 计算阅读时间
    const content = typeof body.content === 'string' ? body.content : body.content.en || '';
    const wordsPerMinute = 200;
    const words = content.trim().split(/\s+/).length;
    const readingTime = Math.ceil(words / wordsPerMinute);

    const articleData = {
      ...body,
      readingTime,
      status: body.status || 'DRAFT',
      featured: body.featured || false,
    };

    const article = await ContentManager.createArticle(articleData);

    return NextResponse.json(article, { status: 201 });
  } catch (error) {
    console.error('Create article API error:', error);
    return NextResponse.json(
      { error: 'Failed to create article' },
      { status: 500 }
    );
  }
}
