import { prisma } from '@/lib/prisma';
import { AIService } from '@/lib/ai/ai-service';
import { getPromptTemplate } from '@/lib/ai/prompts';
import type { Locale } from '@/types';

// 测试流程状态
export enum TestFlowState {
  INIT = 'init',
  STARTED = 'started',
  IN_PROGRESS = 'in_progress',
  ANALYZING = 'analyzing',
  COMPLETED = 'completed',
  ERROR = 'error',
}

// 测试答案接口
export interface TestAnswer {
  questionId: string;
  answer: string | string[] | number;
  timestamp: Date;
}

// 测试结果接口
export interface TestResult {
  id: string;
  summary: string;
  details: ResultDetail[];
  recommendations: string[];
  aiInsights: string;
  shareableContent: ShareableContent;
  score?: number;
  traits?: string[];
}

export interface ResultDetail {
  category: string;
  title: string;
  description: string;
  score: number;
  interpretation: string;
}

export interface ShareableContent {
  title: string;
  description: string;
  image?: string;
  hashtags: string[];
}

// 测试会话管理器
export class TestFlowManager {
  private aiService: AIService;

  constructor() {
    this.aiService = AIService.getInstance();
  }

  // 开始新的测试会话
  async startTestSession(
    testId: string,
    userId?: string,
    sessionId?: string,
    locale: Locale = 'en'
  ): Promise<string> {
    try {
      // 获取测试信息
      const test = await prisma.mysticalTest.findUnique({
        where: { id: testId },
      });

      if (!test) {
        throw new Error('Test not found');
      }

      // 创建测试会话
      const testSession = await prisma.testSession.create({
        data: {
          testId,
          userId,
          sessionId: sessionId || this.generateSessionId(),
          answers: [],
          createdAt: new Date(),
        },
      });

      return testSession.id;
    } catch (error) {
      console.error('Error starting test session:', error);
      throw error;
    }
  }

  // 提交答案
  async submitAnswer(
    sessionId: string,
    questionId: string,
    answer: string | string[] | number
  ): Promise<void> {
    try {
      const session = await prisma.testSession.findUnique({
        where: { id: sessionId },
      });

      if (!session) {
        throw new Error('Test session not found');
      }

      const currentAnswers = session.answers as TestAnswer[];
      const newAnswer: TestAnswer = {
        questionId,
        answer,
        timestamp: new Date(),
      };

      // 更新或添加答案
      const existingIndex = currentAnswers.findIndex(a => a.questionId === questionId);
      if (existingIndex >= 0) {
        currentAnswers[existingIndex] = newAnswer;
      } else {
        currentAnswers.push(newAnswer);
      }

      await prisma.testSession.update({
        where: { id: sessionId },
        data: {
          answers: currentAnswers,
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      console.error('Error submitting answer:', error);
      throw error;
    }
  }

  // 完成测试并生成结果
  async completeTest(sessionId: string): Promise<TestResult> {
    try {
      const session = await prisma.testSession.findUnique({
        where: { id: sessionId },
        include: {
          test: true,
          user: true,
        },
      });

      if (!session) {
        throw new Error('Test session not found');
      }

      // 更新会话状态为分析中
      await prisma.testSession.update({
        where: { id: sessionId },
        data: { updatedAt: new Date() },
      });

      // 生成AI分析
      const aiAnalysis = await this.generateAIAnalysis(session);

      // 创建测试结果
      const result: TestResult = {
        id: this.generateResultId(),
        summary: aiAnalysis.summary,
        details: aiAnalysis.details,
        recommendations: aiAnalysis.recommendations,
        aiInsights: aiAnalysis.insights,
        shareableContent: aiAnalysis.shareableContent,
        score: aiAnalysis.score,
        traits: aiAnalysis.traits,
      };

      // 保存结果到数据库
      await prisma.testSession.update({
        where: { id: sessionId },
        data: {
          result: result,
          completedAt: new Date(),
        },
      });

      return result;
    } catch (error) {
      console.error('Error completing test:', error);
      throw error;
    }
  }

  // 获取测试结果
  async getTestResult(sessionId: string): Promise<TestResult | null> {
    try {
      const session = await prisma.testSession.findUnique({
        where: { id: sessionId },
      });

      if (!session || !session.result) {
        return null;
      }

      return session.result as TestResult;
    } catch (error) {
      console.error('Error getting test result:', error);
      throw error;
    }
  }

  // 生成分享链接
  async generateShareLink(sessionId: string): Promise<string> {
    try {
      const shareToken = this.generateShareToken();
      
      await prisma.testSession.update({
        where: { id: sessionId },
        data: { updatedAt: new Date() },
      });

      return `${process.env.NEXT_PUBLIC_SITE_URL}/share/${shareToken}`;
    } catch (error) {
      console.error('Error generating share link:', error);
      throw error;
    }
  }

  // 生成AI分析
  private async generateAIAnalysis(session: any): Promise<any> {
    try {
      const testType = session.test.type.toLowerCase();
      const answers = session.answers as TestAnswer[];
      const locale = 'en'; // 可以从session或用户偏好获取

      // 获取提示词模板
      const promptTemplate = getPromptTemplate(testType, locale);
      
      // 构建用户上下文
      const userContext = this.buildUserContext(answers, session.test);
      
      // 生成AI提示词
      const prompt = this.buildPrompt(promptTemplate, userContext, testType);

      // 调用AI服务
      const aiResponse = await this.aiService.generateResponse({
        type: `${testType}_analysis` as any,
        prompt,
        context: userContext,
        locale,
        userId: session.userId,
        sessionId: session.id,
        maxTokens: 2000,
        temperature: 0.7,
      });

      // 解析AI响应
      return this.parseAIResponse(aiResponse.content, testType);
    } catch (error) {
      console.error('Error generating AI analysis:', error);
      // 返回默认结果
      return this.getDefaultResult(session.test.type);
    }
  }

  // 构建用户上下文
  private buildUserContext(answers: TestAnswer[], test: any): Record<string, any> {
    const context: Record<string, any> = {
      testType: test.type,
      testName: test.name,
      answers: {},
      answerCount: answers.length,
      completionTime: new Date(),
    };

    // 将答案转换为易于处理的格式
    answers.forEach(answer => {
      context.answers[answer.questionId] = answer.answer;
    });

    return context;
  }

  // 构建AI提示词
  private buildPrompt(template: any, context: Record<string, any>, testType: string): string {
    let prompt = template.user || template.prompt || '';
    
    // 替换模板变量
    prompt = prompt.replace('{testType}', testType);
    prompt = prompt.replace('{answers}', JSON.stringify(context.answers, null, 2));
    prompt = prompt.replace('{answerCount}', context.answerCount.toString());
    
    return prompt;
  }

  // 解析AI响应
  private parseAIResponse(content: string, testType: string): any {
    try {
      // 尝试解析JSON格式的响应
      const parsed = JSON.parse(content);
      return parsed;
    } catch {
      // 如果不是JSON，则解析文本格式
      return this.parseTextResponse(content, testType);
    }
  }

  // 解析文本格式的AI响应
  private parseTextResponse(content: string, testType: string): any {
    const lines = content.split('\n').filter(line => line.trim());
    
    return {
      summary: lines[0] || 'Analysis completed',
      details: [
        {
          category: 'General',
          title: 'Analysis Result',
          description: content,
          score: 75,
          interpretation: 'Based on your responses, here is your personalized analysis.',
        },
      ],
      recommendations: [
        'Continue exploring your spiritual journey',
        'Practice mindfulness and self-reflection',
        'Trust your intuition',
      ],
      insights: content,
      shareableContent: {
        title: `My ${testType} Test Result`,
        description: lines[0] || 'Discover your mystical insights',
        hashtags: ['#mystical', '#spirituality', `#${testType}`],
      },
      score: 75,
      traits: ['intuitive', 'spiritual', 'insightful'],
    };
  }

  // 获取默认结果
  private getDefaultResult(testType: string): any {
    return {
      summary: 'Thank you for completing the test!',
      details: [
        {
          category: 'General',
          title: 'Test Completed',
          description: 'Your responses have been recorded and analyzed.',
          score: 70,
          interpretation: 'Based on your responses, you show great potential for spiritual growth.',
        },
      ],
      recommendations: [
        'Continue your spiritual journey',
        'Practice meditation regularly',
        'Trust your inner wisdom',
      ],
      insights: 'Your responses indicate a strong connection to your inner self and spiritual awareness.',
      shareableContent: {
        title: `My ${testType} Test Result`,
        description: 'Discover your mystical insights',
        hashtags: ['#mystical', '#spirituality', `#${testType}`],
      },
      score: 70,
      traits: ['spiritual', 'intuitive'],
    };
  }

  // 辅助方法
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateResultId(): string {
    return `result_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateShareToken(): string {
    return `share_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
