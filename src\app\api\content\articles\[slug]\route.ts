import { NextRequest, NextResponse } from 'next/server';
import { ContentManager } from '@/lib/content/content-manager';
import type { Locale } from '@/types';

// GET /api/content/articles/[slug] - 获取单篇文章
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const locale = searchParams.get('locale') as Locale;
    
    const article = await ContentManager.getArticle(params.slug, locale);
    
    if (!article) {
      return NextResponse.json(
        { error: 'Article not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(article);
  } catch (error) {
    console.error('Article API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch article' },
      { status: 500 }
    );
  }
}

// PUT /api/content/articles/[slug] - 更新文章
export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const body = await request.json();
    
    // 首先获取现有文章
    const existingArticle = await ContentManager.getArticle(params.slug);
    if (!existingArticle) {
      return NextResponse.json(
        { error: 'Article not found' },
        { status: 404 }
      );
    }

    // 如果内容更新了，重新计算阅读时间
    let readingTime = existingArticle.readingTime;
    if (body.content) {
      const content = typeof body.content === 'string' ? body.content : body.content.en || '';
      const wordsPerMinute = 200;
      const words = content.trim().split(/\s+/).length;
      readingTime = Math.ceil(words / wordsPerMinute);
    }

    const updateData = {
      ...body,
      readingTime,
    };

    const article = await ContentManager.updateArticle(existingArticle.id, updateData);

    return NextResponse.json(article);
  } catch (error) {
    console.error('Update article API error:', error);
    return NextResponse.json(
      { error: 'Failed to update article' },
      { status: 500 }
    );
  }
}

// DELETE /api/content/articles/[slug] - 删除文章
export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const article = await ContentManager.getArticle(params.slug);
    if (!article) {
      return NextResponse.json(
        { error: 'Article not found' },
        { status: 404 }
      );
    }

    await ContentManager.deleteArticle(article.id);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Delete article API error:', error);
    return NextResponse.json(
      { error: 'Failed to delete article' },
      { status: 500 }
    );
  }
}
