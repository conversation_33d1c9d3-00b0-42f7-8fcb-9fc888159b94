import { NextRequest, NextResponse } from 'next/server';
import { ContentManager } from '@/lib/content/content-manager';
import type { Locale } from '@/types';

// GET /api/content/search - 搜索内容
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const query = searchParams.get('q');
    if (!query || query.trim().length < 2) {
      return NextResponse.json(
        { error: 'Search query must be at least 2 characters long' },
        { status: 400 }
      );
    }

    const type = searchParams.get('type') as any || 'article';
    const locale = searchParams.get('locale') as Locale || 'en';
    const limit = parseInt(searchParams.get('limit') || '10');

    const results = await ContentManager.searchContent(query, type, locale, limit);

    return NextResponse.json({
      query,
      type,
      locale,
      results,
      total: results.length,
    });
  } catch (error) {
    console.error('Search API error:', error);
    return NextResponse.json(
      { error: 'Search failed' },
      { status: 500 }
    );
  }
}

// POST /api/content/search - 高级搜索
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { query, filters = {}, locale = 'en', limit = 10 } = body;

    if (!query || query.trim().length < 2) {
      return NextResponse.json(
        { error: 'Search query must be at least 2 characters long' },
        { status: 400 }
      );
    }

    // 执行多类型搜索
    const [articles, products] = await Promise.all([
      ContentManager.searchContent(query, 'article', locale, limit),
      ContentManager.searchContent(query, 'product', locale, limit),
    ]);

    // 应用过滤器
    let filteredArticles = articles;
    let filteredProducts = products;

    if (filters.category) {
      filteredArticles = articles.filter(article => 
        article.category?.slug === filters.category
      );
      filteredProducts = products.filter(product => 
        product.category?.slug === filters.category
      );
    }

    if (filters.dateRange) {
      const { start, end } = filters.dateRange;
      const startDate = new Date(start);
      const endDate = new Date(end);
      
      filteredArticles = filteredArticles.filter(article => {
        const publishedAt = new Date(article.publishedAt);
        return publishedAt >= startDate && publishedAt <= endDate;
      });
    }

    if (filters.featured !== undefined) {
      filteredArticles = filteredArticles.filter(article => 
        article.featured === filters.featured
      );
      filteredProducts = filteredProducts.filter(product => 
        product.featured === filters.featured
      );
    }

    return NextResponse.json({
      query,
      filters,
      locale,
      results: {
        articles: filteredArticles,
        products: filteredProducts,
      },
      total: {
        articles: filteredArticles.length,
        products: filteredProducts.length,
        all: filteredArticles.length + filteredProducts.length,
      },
    });
  } catch (error) {
    console.error('Advanced search API error:', error);
    return NextResponse.json(
      { error: 'Advanced search failed' },
      { status: 500 }
    );
  }
}
