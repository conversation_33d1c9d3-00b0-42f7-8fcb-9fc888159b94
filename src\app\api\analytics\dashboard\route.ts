import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '7d'; // 7d, 30d, 90d

    // 计算日期范围
    const now = new Date();
    const startDate = new Date();
    
    switch (period) {
      case '7d':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(now.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(now.getDate() - 90);
        break;
      default:
        startDate.setDate(now.getDate() - 7);
    }

    // 并行获取各种统计数据
    const [
      totalPageViews,
      uniqueVisitors,
      topPages,
      topEvents,
      aiUsageStats,
      testCompletions,
      errorStats,
      performanceStats,
    ] = await Promise.all([
      // 总页面浏览量
      prisma.analytics.count({
        where: {
          event: 'page_view',
          timestamp: { gte: startDate },
        },
      }),

      // 独立访客数（基于IP）
      prisma.analytics.groupBy({
        by: ['clientIP'],
        where: {
          event: 'page_view',
          timestamp: { gte: startDate },
        },
        _count: true,
      }),

      // 热门页面
      prisma.analytics.groupBy({
        by: ['url'],
        where: {
          event: 'page_view',
          timestamp: { gte: startDate },
        },
        _count: true,
        orderBy: {
          _count: {
            url: 'desc',
          },
        },
        take: 10,
      }),

      // 热门事件
      prisma.analytics.groupBy({
        by: ['event'],
        where: {
          timestamp: { gte: startDate },
        },
        _count: true,
        orderBy: {
          _count: {
            event: 'desc',
          },
        },
        take: 10,
      }),

      // AI使用统计
      prisma.analytics.count({
        where: {
          event: 'ai_usage',
          timestamp: { gte: startDate },
        },
      }),

      // 测试完成统计
      prisma.analytics.count({
        where: {
          event: 'test_completion',
          timestamp: { gte: startDate },
        },
      }),

      // 错误统计
      prisma.analytics.count({
        where: {
          event: 'error',
          timestamp: { gte: startDate },
        },
      }),

      // 性能统计
      prisma.analytics.findMany({
        where: {
          event: 'performance',
          timestamp: { gte: startDate },
        },
        select: {
          parameters: true,
        },
      }),
    ]);

    // 处理性能数据
    const performanceMetrics = performanceStats.reduce((acc: any, stat) => {
      const params = stat.parameters as any;
      if (params.metric_name && params.metric_value) {
        if (!acc[params.metric_name]) {
          acc[params.metric_name] = [];
        }
        acc[params.metric_name].push(params.metric_value);
      }
      return acc;
    }, {});

    // 计算性能平均值
    const avgPerformance = Object.entries(performanceMetrics).reduce((acc: any, [metric, values]: [string, any]) => {
      acc[metric] = values.reduce((sum: number, val: number) => sum + val, 0) / values.length;
      return acc;
    }, {});

    // 获取每日趋势数据
    const dailyTrends = await getDailyTrends(startDate, now);

    // 构建响应数据
    const dashboardData = {
      period,
      summary: {
        totalPageViews,
        uniqueVisitors: uniqueVisitors.length,
        aiUsage: aiUsageStats,
        testCompletions,
        errors: errorStats,
      },
      topPages: topPages.map(page => ({
        url: page.url,
        views: page._count,
      })),
      topEvents: topEvents.map(event => ({
        event: event.event,
        count: event._count,
      })),
      performance: avgPerformance,
      trends: dailyTrends,
      lastUpdated: new Date().toISOString(),
    };

    return NextResponse.json(dashboardData);

  } catch (error) {
    console.error('Dashboard API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard data' },
      { status: 500 }
    );
  }
}

// 获取每日趋势数据
async function getDailyTrends(startDate: Date, endDate: Date) {
  try {
    const trends = await prisma.$queryRaw`
      SELECT 
        DATE(timestamp) as date,
        COUNT(CASE WHEN event = 'page_view' THEN 1 END) as page_views,
        COUNT(CASE WHEN event = 'ai_usage' THEN 1 END) as ai_usage,
        COUNT(CASE WHEN event = 'test_completion' THEN 1 END) as test_completions,
        COUNT(CASE WHEN event = 'error' THEN 1 END) as errors
      FROM Analytics 
      WHERE timestamp >= ${startDate} AND timestamp <= ${endDate}
      GROUP BY DATE(timestamp)
      ORDER BY date ASC
    `;

    return trends;
  } catch (error) {
    console.error('Failed to fetch daily trends:', error);
    return [];
  }
}
