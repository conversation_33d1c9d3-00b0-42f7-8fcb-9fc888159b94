import { NextRequest, NextResponse } from 'next/server';
import { TestFlowManager } from '@/lib/tests/test-flow-manager';
import { z } from 'zod';

const submitAnswerSchema = z.object({
  questionId: z.string(),
  answer: z.union([z.string(), z.array(z.string()), z.number()]),
});

// POST /api/tests/sessions/[sessionId]/answer - 提交答案
export async function POST(
  request: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    const { sessionId } = params;
    const body = await request.json();
    const { questionId, answer } = submitAnswerSchema.parse(body);

    const testFlowManager = new TestFlowManager();
    await testFlowManager.submitAnswer(sessionId, questionId, answer);

    return NextResponse.json({
      success: true,
      message: 'Answer submitted successfully',
    });
  } catch (error) {
    console.error('Error submitting answer:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to submit answer',
      },
      { status: 500 }
    );
  }
}
