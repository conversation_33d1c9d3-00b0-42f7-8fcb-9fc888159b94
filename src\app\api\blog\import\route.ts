import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import matter from 'gray-matter';

// 导入配置验证
const importConfigSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  files: z.array(z.object({
    name: z.string(),
    content: z.string(),
    path: z.string().optional(),
  })),
  options: z.object({
    autoPublish: z.boolean().optional().default(false),
    generateSlug: z.boolean().optional().default(true),
    optimizeImages: z.boolean().optional().default(true),
    extractKeywords: z.boolean().optional().default(true),
    seoAnalysis: z.boolean().optional().default(true),
    duplicateCheck: z.boolean().optional().default(true),
    defaultCategory: z.string().optional(),
    defaultAuthor: z.string().optional(),
  }).optional().default({}),
});

// POST /api/blog/import - 批量导入文章
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description, files, options } = importConfigSchema.parse(body);

    // 获取用户ID（需要实现身份验证）
    const createdBy = 'user-id'; // 临时硬编码

    // 创建导入任务
    const importTask = await prisma.importTask.create({
      data: {
        name,
        description,
        totalFiles: files.length,
        config: { options },
        createdBy,
        status: 'PROCESSING',
        startedAt: new Date(),
      },
    });

    // 处理文件导入
    const results = await processImportFiles(importTask.id, files, options);

    // 更新任务状态
    await prisma.importTask.update({
      where: { id: importTask.id },
      data: {
        status: 'COMPLETED',
        processedFiles: results.length,
        successCount: results.filter(r => r.success).length,
        errorCount: results.filter(r => !r.success).length,
        results: results,
        completedAt: new Date(),
      },
    });

    return NextResponse.json({
      taskId: importTask.id,
      results,
      summary: {
        total: files.length,
        success: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
      },
    });
  } catch (error) {
    console.error('Error importing articles:', error);
    return NextResponse.json(
      { error: 'Failed to import articles' },
      { status: 500 }
    );
  }
}

// 处理文件导入
async function processImportFiles(
  taskId: string,
  files: any[],
  options: any
): Promise<any[]> {
  const results = [];

  for (const file of files) {
    try {
      // 创建导入项目记录
      const importItem = await prisma.importItem.create({
        data: {
          taskId,
          filePath: file.path || file.name,
          fileName: file.name,
          status: 'PROCESSING',
          metadata: { originalSize: file.content.length },
        },
      });

      // 解析Markdown文件
      const parsed = parseMarkdownFile(file.content);

      // 生成slug
      const slug = options.generateSlug 
        ? generateSlug(parsed.frontmatter.title || file.name)
        : parsed.frontmatter.slug;

      // 检查重复
      if (options.duplicateCheck) {
        const existing = await prisma.article.findUnique({
          where: { slug },
        });
        if (existing) {
          await prisma.importItem.update({
            where: { id: importItem.id },
            data: {
              status: 'SKIPPED',
              error: 'Article with this slug already exists',
              processedAt: new Date(),
            },
          });
          results.push({
            success: false,
            fileName: file.name,
            error: 'Duplicate slug',
          });
          continue;
        }
      }

      // 计算阅读时间
      const readingTime = calculateReadingTime(parsed.content);

      // 创建文章
      const article = await prisma.article.create({
        data: {
          slug,
          title: { en: parsed.frontmatter.title },
          content: { en: parsed.content },
          excerpt: parsed.frontmatter.description 
            ? { en: parsed.frontmatter.description }
            : undefined,
          coverImage: parsed.frontmatter.coverImage,
          authorId: options.defaultAuthor || 'user-id',
          categoryId: options.defaultCategory || 'default-category',
          status: options.autoPublish ? 'PUBLISHED' : 'DRAFT',
          publishedAt: options.autoPublish ? new Date() : undefined,
          readingTime,
          featured: parsed.frontmatter.featured || false,
          seoTitle: parsed.frontmatter.seoTitle 
            ? { en: parsed.frontmatter.seoTitle }
            : undefined,
          seoDescription: parsed.frontmatter.seoDescription
            ? { en: parsed.frontmatter.seoDescription }
            : undefined,
          seoKeywords: parsed.frontmatter.keywords
            ? { en: parsed.frontmatter.keywords }
            : undefined,
          aiGenerated: parsed.frontmatter.aiGenerated || false,
          aiMetadata: parsed.frontmatter.aiMetadata,
          originalFile: file.path,
          importedAt: new Date(),
        },
      });

      // 更新导入项目状态
      await prisma.importItem.update({
        where: { id: importItem.id },
        data: {
          status: 'SUCCESS',
          articleId: article.id,
          processedAt: new Date(),
        },
      });

      results.push({
        success: true,
        fileName: file.name,
        articleId: article.id,
        slug: article.slug,
      });

    } catch (error) {
      // 更新导入项目状态为失败
      await prisma.importItem.update({
        where: { id: importItem.id },
        data: {
          status: 'FAILED',
          error: error.message,
          processedAt: new Date(),
        },
      });

      results.push({
        success: false,
        fileName: file.name,
        error: error.message,
      });
    }
  }

  return results;
}

// 解析Markdown文件
function parseMarkdownFile(content: string) {
  const { data: frontmatter, content: markdownContent } = matter(content);
  
  return {
    frontmatter,
    content: markdownContent,
  };
}

// 生成slug
function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

// 计算阅读时间
function calculateReadingTime(content: string): number {
  const wordsPerMinute = 200;
  const wordCount = content.split(/\s+/).length;
  return Math.ceil(wordCount / wordsPerMinute);
}

// GET /api/blog/import/[taskId] - 获取导入任务状态
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const taskId = url.pathname.split('/').pop();

    if (!taskId) {
      return NextResponse.json(
        { error: 'Task ID is required' },
        { status: 400 }
      );
    }

    const task = await prisma.importTask.findUnique({
      where: { id: taskId },
      include: {
        items: true,
        creator: { select: { id: true, name: true } },
      },
    });

    if (!task) {
      return NextResponse.json(
        { error: 'Import task not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(task);
  } catch (error) {
    console.error('Error fetching import task:', error);
    return NextResponse.json(
      { error: 'Failed to fetch import task' },
      { status: 500 }
    );
  }
}
