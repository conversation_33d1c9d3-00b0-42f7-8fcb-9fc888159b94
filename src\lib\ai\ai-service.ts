import type { Locale } from '@/types';

// AI服务提供商类型
export type AIProvider = 'qwen' | 'doubao' | 'zhipu';

// AI请求类型
export type AIRequestType = 
  | 'tarot_reading' 
  | 'astrology_analysis' 
  | 'numerology_calculation' 
  | 'general_mystical'
  | 'daily_horoscope'
  | 'love_compatibility'
  | 'career_guidance';

// AI请求接口
export interface AIRequest {
  type: AIRequestType;
  prompt: string;
  context?: Record<string, any>;
  locale: Locale;
  userId?: string;
  sessionId?: string;
  maxTokens?: number;
  temperature?: number;
}

// AI响应接口
export interface AIResponse {
  content: string;
  provider: AIProvider;
  model: string;
  tokens: number;
  cost: number;
  timestamp: Date;
  success: boolean;
  error?: string;
}

// AI服务配置
interface AIServiceConfig {
  provider: AIProvider;
  apiKey: string;
  endpoint: string;
  model: string;
  maxTokens: number;
  temperature: number;
  timeout: number;
}

// AI服务管理器
export class AIServiceManager {
  private configs: Map<AIProvider, AIServiceConfig> = new Map();
  private fallbackOrder: AIProvider[] = ['qwen', 'zhipu', 'doubao'];

  constructor() {
    this.initializeConfigs();
  }

  private initializeConfigs() {
    // 通义千问配置
    if (process.env.QWEN_API_KEY) {
      this.configs.set('qwen', {
        provider: 'qwen',
        apiKey: process.env.QWEN_API_KEY,
        endpoint: process.env.QWEN_API_ENDPOINT || 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
        model: 'qwen-turbo',
        maxTokens: 2000,
        temperature: 0.7,
        timeout: 30000,
      });
    }

    // 智谱AI配置
    if (process.env.ZHIPU_API_KEY) {
      this.configs.set('zhipu', {
        provider: 'zhipu',
        apiKey: process.env.ZHIPU_API_KEY,
        endpoint: process.env.ZHIPU_API_ENDPOINT || 'https://open.bigmodel.cn/api/paas/v4/',
        model: 'glm-4',
        maxTokens: 2000,
        temperature: 0.7,
        timeout: 30000,
      });
    }

    // 豆包配置
    if (process.env.DOUBAO_API_KEY) {
      this.configs.set('doubao', {
        provider: 'doubao',
        apiKey: process.env.DOUBAO_API_KEY,
        endpoint: process.env.DOUBAO_API_ENDPOINT || '',
        model: 'doubao-pro',
        maxTokens: 2000,
        temperature: 0.7,
        timeout: 30000,
      });
    }
  }

  // 发送AI请求
  async sendRequest(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now();
    let lastError: Error | null = null;

    // 尝试每个可用的AI服务
    for (const provider of this.fallbackOrder) {
      const config = this.configs.get(provider);
      if (!config) continue;

      try {
        console.log(`Attempting AI request with ${provider}...`);
        const response = await this.callAIService(config, request);
        
        // 记录成功的请求
        await this.logAIRequest(request, response, true);
        
        return response;
      } catch (error) {
        console.warn(`AI request failed with ${provider}:`, error);
        lastError = error as Error;
        
        // 记录失败的请求
        await this.logAIRequest(request, null, false, error as Error);
        
        // 继续尝试下一个服务
        continue;
      }
    }

    // 所有服务都失败了
    throw new Error(`All AI services failed. Last error: ${lastError?.message}`);
  }

  // 调用具体的AI服务
  private async callAIService(config: AIServiceConfig, request: AIRequest): Promise<AIResponse> {
    const { provider } = config;
    
    switch (provider) {
      case 'qwen':
        return this.callQwenAPI(config, request);
      case 'zhipu':
        return this.callZhipuAPI(config, request);
      case 'doubao':
        return this.callDoubaoAPI(config, request);
      default:
        throw new Error(`Unsupported AI provider: ${provider}`);
    }
  }

  // 通义千问API调用
  private async callQwenAPI(config: AIServiceConfig, request: AIRequest): Promise<AIResponse> {
    const response = await fetch(config.endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`,
      },
      body: JSON.stringify({
        model: config.model,
        input: {
          messages: [
            {
              role: 'user',
              content: request.prompt,
            },
          ],
        },
        parameters: {
          max_tokens: request.maxTokens || config.maxTokens,
          temperature: request.temperature || config.temperature,
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Qwen API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.output?.text) {
      return {
        content: data.output.text,
        provider: 'qwen',
        model: config.model,
        tokens: data.usage?.total_tokens || 0,
        cost: this.calculateCost('qwen', data.usage?.total_tokens || 0),
        timestamp: new Date(),
        success: true,
      };
    } else {
      throw new Error('Invalid response from Qwen API');
    }
  }

  // 智谱AI API调用
  private async callZhipuAPI(config: AIServiceConfig, request: AIRequest): Promise<AIResponse> {
    const response = await fetch(`${config.endpoint}chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`,
      },
      body: JSON.stringify({
        model: config.model,
        messages: [
          {
            role: 'user',
            content: request.prompt,
          },
        ],
        max_tokens: request.maxTokens || config.maxTokens,
        temperature: request.temperature || config.temperature,
      }),
    });

    if (!response.ok) {
      throw new Error(`Zhipu API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.choices?.[0]?.message?.content) {
      return {
        content: data.choices[0].message.content,
        provider: 'zhipu',
        model: config.model,
        tokens: data.usage?.total_tokens || 0,
        cost: this.calculateCost('zhipu', data.usage?.total_tokens || 0),
        timestamp: new Date(),
        success: true,
      };
    } else {
      throw new Error('Invalid response from Zhipu API');
    }
  }

  // 豆包API调用（示例实现）
  private async callDoubaoAPI(config: AIServiceConfig, request: AIRequest): Promise<AIResponse> {
    // 这里需要根据豆包的实际API文档来实现
    throw new Error('Doubao API not implemented yet');
  }

  // 计算成本
  private calculateCost(provider: AIProvider, tokens: number): number {
    const costPerToken = {
      qwen: 0.0001, // 示例价格
      zhipu: 0.0001,
      doubao: 0.0001,
    };

    return tokens * (costPerToken[provider] || 0);
  }

  // 记录AI请求
  private async logAIRequest(
    request: AIRequest,
    response: AIResponse | null,
    success: boolean,
    error?: Error
  ): Promise<void> {
    try {
      // 这里可以记录到数据库或日志系统
      const logData = {
        type: request.type,
        locale: request.locale,
        userId: request.userId,
        sessionId: request.sessionId,
        provider: response?.provider,
        model: response?.model,
        tokens: response?.tokens || 0,
        cost: response?.cost || 0,
        success,
        error: error?.message,
        timestamp: new Date(),
      };

      // 发送到日志API
      await fetch('/api/ai/log', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(logData),
      }).catch(() => {
        // 静默失败，不影响主要功能
      });
    } catch (error) {
      console.error('Failed to log AI request:', error);
    }
  }

  // 获取可用的AI服务
  getAvailableProviders(): AIProvider[] {
    return Array.from(this.configs.keys());
  }

  // 检查服务健康状态
  async checkHealth(): Promise<Record<AIProvider, boolean>> {
    const health: Record<AIProvider, boolean> = {} as any;
    
    for (const [provider, config] of this.configs) {
      try {
        // 发送简单的测试请求
        await this.callAIService(config, {
          type: 'general_mystical',
          prompt: 'Hello',
          locale: 'en',
          maxTokens: 10,
          temperature: 0.1,
        });
        health[provider] = true;
      } catch (error) {
        health[provider] = false;
      }
    }
    
    return health;
  }
}

// 全局AI服务实例
export const aiService = new AIServiceManager();

// 导出便捷函数
export async function generateMysticalContent(
  type: AIRequestType,
  prompt: string,
  locale: Locale,
  context?: Record<string, any>
): Promise<string> {
  const response = await aiService.sendRequest({
    type,
    prompt,
    locale,
    context,
  });
  
  return response.content;
}

export default aiService;
