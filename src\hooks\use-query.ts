'use client';

import { useState, useEffect, useCallback, useRef } from 'react';

export interface QueryOptions<T> {
  enabled?: boolean;
  refetchOnWindowFocus?: boolean;
  staleTime?: number;
  cacheTime?: number;
  retry?: number;
  retryDelay?: number;
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
}

export interface QueryResult<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
  isStale: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface PaginationOptions {
  initialPage?: number;
  initialLimit?: number;
  enabled?: boolean;
}

export interface PaginatedQueryResult<T> {
  data: T[];
  loading: boolean;
  error: Error | null;
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  nextPage: () => void;
  previousPage: () => void;
  goToPage: (page: number) => void;
  refetch: () => Promise<void>;
}

// 简单的内存缓存
const cache = new Map<string, { data: any; timestamp: number; staleTime: number }>();

const getCacheKey = (key: string | string[]): string => {
  return Array.isArray(key) ? key.join(':') : key;
};

const getCachedData = <T>(key: string, staleTime: number): T | null => {
  const cacheKey = getCacheKey(key);
  const cached = cache.get(cacheKey);
  
  if (!cached) return null;
  
  const isStale = Date.now() - cached.timestamp > staleTime;
  if (isStale) {
    cache.delete(cacheKey);
    return null;
  }
  
  return cached.data;
};

const setCachedData = <T>(key: string, data: T, staleTime: number): void => {
  const cacheKey = getCacheKey(key);
  cache.set(cacheKey, {
    data,
    timestamp: Date.now(),
    staleTime,
  });
};

// 通用数据获取Hook
export const useQuery = <T>(
  key: string | string[],
  fetcher: () => Promise<T>,
  options: QueryOptions<T> = {}
): QueryResult<T> => {
  const {
    enabled = true,
    refetchOnWindowFocus = false,
    staleTime = 5 * 60 * 1000, // 5分钟
    cacheTime = 10 * 60 * 1000, // 10分钟
    retry = 3,
    retryDelay = 1000,
    onSuccess,
    onError,
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isStale, setIsStale] = useState(false);
  
  const retryCountRef = useRef(0);
  const cacheKey = getCacheKey(key);

  const fetchData = useCallback(async () => {
    if (!enabled) return;

    // 检查缓存
    const cachedData = getCachedData<T>(cacheKey, staleTime);
    if (cachedData && !isStale) {
      setData(cachedData);
      setIsStale(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await fetcher();
      setData(result);
      setError(null);
      setIsStale(false);
      retryCountRef.current = 0;
      
      // 缓存数据
      setCachedData(cacheKey, result, staleTime);
      
      onSuccess?.(result);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      
      if (retryCountRef.current < retry) {
        retryCountRef.current++;
        setTimeout(() => {
          fetchData();
        }, retryDelay * retryCountRef.current);
      } else {
        setError(error);
        onError?.(error);
      }
    } finally {
      setLoading(false);
    }
  }, [enabled, cacheKey, staleTime, isStale, fetcher, retry, retryDelay, onSuccess, onError]);

  const refetch = useCallback(async () => {
    setIsStale(true);
    await fetchData();
  }, [fetchData]);

  // 初始加载
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 窗口焦点重新获取
  useEffect(() => {
    if (!refetchOnWindowFocus) return;

    const handleFocus = () => {
      if (data) {
        setIsStale(true);
        fetchData();
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [refetchOnWindowFocus, data, fetchData]);

  return {
    data,
    loading,
    error,
    refetch,
    isStale,
  };
};

// 分页数据Hook
export const usePaginatedQuery = <T>(
  key: string,
  fetcher: (page: number, limit: number) => Promise<PaginatedResponse<T>>,
  options: PaginationOptions = {}
): PaginatedQueryResult<T> => {
  const {
    initialPage = 1,
    initialLimit = 10,
    enabled = true,
  } = options;

  const [page, setPage] = useState(initialPage);
  const [limit, setLimit] = useState(initialLimit);

  const queryKey = `${key}:${page}:${limit}`;
  
  const { data: response, loading, error, refetch } = useQuery(
    queryKey,
    () => fetcher(page, limit),
    { enabled }
  );

  const data = response?.data || [];
  const total = response?.total || 0;
  const totalPages = response?.totalPages || 0;

  const hasNextPage = page < totalPages;
  const hasPreviousPage = page > 1;

  const nextPage = useCallback(() => {
    if (hasNextPage) {
      setPage(prev => prev + 1);
    }
  }, [hasNextPage]);

  const previousPage = useCallback(() => {
    if (hasPreviousPage) {
      setPage(prev => prev - 1);
    }
  }, [hasPreviousPage]);

  const goToPage = useCallback((newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
    }
  }, [totalPages]);

  return {
    data,
    loading,
    error,
    page,
    limit,
    total,
    totalPages,
    hasNextPage,
    hasPreviousPage,
    nextPage,
    previousPage,
    goToPage,
    refetch,
  };
};
