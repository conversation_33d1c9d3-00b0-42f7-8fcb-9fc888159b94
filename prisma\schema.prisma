// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户模型
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  avatar    String?
  role      Role     @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 用户偏好设置
  preferences UserPreferences?

  // 订阅信息
  subscription Subscription?

  // 关联的内容
  articles    Article[]
  comments    Comment[]
  testSessions TestSession[]
  orders      Order[]
  articleRevisions ArticleRevision[]
  importTasks ImportTask[]

  @@map("users")
}

// 用户角色枚举
enum Role {
  USER
  EDITOR
  ADMIN
}

// 用户偏好设置
model UserPreferences {
  id           String  @id @default(cuid())
  userId       String  @unique
  locale       String  @default("en")
  theme        String  @default("system")
  notifications <PERSON><PERSON><PERSON> @default(true)
  newsletter   Boolean @default(false)

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_preferences")
}

// 订阅模型
model Subscription {
  id        String           @id @default(cuid())
  userId    String           @unique
  plan      SubscriptionPlan @default(FREE)
  status    SubscriptionStatus @default(ACTIVE)
  expiresAt DateTime?
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("subscriptions")
}

enum SubscriptionPlan {
  FREE
  PREMIUM
  PRO
}

enum SubscriptionStatus {
  ACTIVE
  INACTIVE
  CANCELLED
}

// 分类模型
model Category {
  id          String @id @default(cuid())
  name        Json   // 多语言名称
  slug        String @unique
  description Json?  // 多语言描述
  color       String?
  icon        String?
  parentId    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联
  parent   Category? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")
  articles Article[]

  @@map("categories")
}

// 标签模型
model Tag {
  id        String @id @default(cuid())
  name      Json   // 多语言名称
  slug      String @unique
  color     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联
  articles Article[]
  products Product[]

  @@map("tags")
}

// 文章模型
model Article {
  id          String        @id @default(cuid())
  slug        String        @unique
  title       Json          // 多语言标题
  content     Json          // 多语言内容
  excerpt     Json?         // 多语言摘要
  coverImage  String?
  images      Json?         // 文章图片信息
  authorId    String
  categoryId  String
  status      ArticleStatus @default(DRAFT)
  publishedAt DateTime?
  scheduledAt DateTime?     // 定时发布时间
  readingTime Int           @default(0)
  views       Int           @default(0)
  featured    Boolean       @default(false)
  seoTitle    Json?         // 多语言SEO标题
  seoDescription Json?      // 多语言SEO描述
  seoKeywords Json?         // 多语言SEO关键词
  seoScore    Int?          // SEO评分
  qualityScore Int?         // 内容质量评分
  aiGenerated Boolean       @default(false) // 是否AI生成
  aiMetadata  Json?         // AI生成元数据
  originalFile String?      // 原始文件路径
  importedAt  DateTime?     // 导入时间
  lastReviewed DateTime?    // 最后审核时间
  reviewedBy  String?       // 审核人
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // 关联
  author   User      @relation(fields: [authorId], references: [id])
  category Category  @relation(fields: [categoryId], references: [id])
  tags     Tag[]
  comments Comment[]
  revisions ArticleRevision[]
  qualityChecks QualityCheck[]
  importItems ImportItem[]

  @@map("articles")
}

enum ArticleStatus {
  DRAFT
  PENDING
  SCHEDULED
  PUBLISHED
  ARCHIVED
  DELETED
}

// 评论模型
model Comment {
  id        String   @id @default(cuid())
  content   String
  authorId  String
  articleId String
  parentId  String?
  approved  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联
  author   User      @relation(fields: [authorId], references: [id])
  article  Article   @relation(fields: [articleId], references: [id], onDelete: Cascade)
  parent   Comment?  @relation("CommentReplies", fields: [parentId], references: [id])
  replies  Comment[] @relation("CommentReplies")

  @@map("comments")
}

// 文章版本模型
model ArticleRevision {
  id        String   @id @default(cuid())
  articleId String
  version   Int
  title     Json     // 版本标题
  content   Json     // 版本内容
  excerpt   Json?    // 版本摘要
  changes   String?  // 变更说明
  createdBy String
  createdAt DateTime @default(now())

  // 关联
  article Article @relation(fields: [articleId], references: [id], onDelete: Cascade)
  author  User    @relation(fields: [createdBy], references: [id])

  @@unique([articleId, version])
  @@map("article_revisions")
}

// 内容质量检查模型
model QualityCheck {
  id          String    @id @default(cuid())
  articleId   String
  checkType   String    // SEO, READABILITY, IMAGES, LINKS, DUPLICATES
  score       Int       // 0-100分
  issues      Json      // 发现的问题
  suggestions Json      // 改进建议
  checkedAt   DateTime  @default(now())
  checkedBy   String?   // 检查人（可能是系统自动检查）

  // 关联
  article Article @relation(fields: [articleId], references: [id], onDelete: Cascade)

  @@map("quality_checks")
}

// 批量导入任务模型
model ImportTask {
  id          String      @id @default(cuid())
  name        String      // 任务名称
  description String?     // 任务描述
  status      ImportStatus @default(PENDING)
  totalFiles  Int         @default(0)
  processedFiles Int      @default(0)
  successCount Int        @default(0)
  errorCount  Int         @default(0)
  config      Json        // 导入配置
  results     Json?       // 导入结果
  errors      Json?       // 错误信息
  createdBy   String
  startedAt   DateTime?
  completedAt DateTime?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // 关联
  creator User @relation(fields: [createdBy], references: [id])
  items   ImportItem[]

  @@map("import_tasks")
}

enum ImportStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
}

// 导入项目模型
model ImportItem {
  id          String       @id @default(cuid())
  taskId      String
  filePath    String       // 原始文件路径
  fileName    String       // 文件名
  status      ImportItemStatus @default(PENDING)
  articleId   String?      // 成功导入后的文章ID
  error       String?      // 错误信息
  metadata    Json?        // 文件元数据
  processedAt DateTime?
  createdAt   DateTime     @default(now())

  // 关联
  task    ImportTask @relation(fields: [taskId], references: [id], onDelete: Cascade)
  article Article?   @relation(fields: [articleId], references: [id])

  @@map("import_items")
}

enum ImportItemStatus {
  PENDING
  PROCESSING
  SUCCESS
  FAILED
  SKIPPED
}

// 商品分类模型
model ProductCategory {
  id          String @id @default(cuid())
  name        Json   // 多语言名称
  slug        String @unique
  description Json?  // 多语言描述
  image       String?
  parentId    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联
  parent   ProductCategory? @relation("ProductCategoryHierarchy", fields: [parentId], references: [id])
  children ProductCategory[] @relation("ProductCategoryHierarchy")
  products Product[]

  @@map("product_categories")
}

// 商品模型
model Product {
  id            String        @id @default(cuid())
  name          Json          // 多语言名称
  description   Json          // 多语言描述
  slug          String        @unique
  price         Decimal       @db.Decimal(10, 2)
  currency      String        @default("USD")
  images        String[]
  categoryId    String
  status        ProductStatus @default(ACTIVE)
  inventory     Int           @default(0)
  sku           String        @unique
  featured      Boolean       @default(false)
  specifications Json?        // 商品规格
  seoTitle      Json?         // 多语言SEO标题
  seoDescription Json?        // 多语言SEO描述
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // 关联
  category  ProductCategory @relation(fields: [categoryId], references: [id])
  tags      Tag[]
  orderItems OrderItem[]

  @@map("products")
}

enum ProductStatus {
  ACTIVE
  INACTIVE
  OUT_OF_STOCK
}

// 订单模型
model Order {
  id          String      @id @default(cuid())
  userId      String
  status      OrderStatus @default(PENDING)
  total       Decimal     @db.Decimal(10, 2)
  currency    String      @default("USD")
  paymentId   String?
  shippingAddress Json?
  billingAddress  Json?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // 关联
  user  User        @relation(fields: [userId], references: [id])
  items OrderItem[]

  @@map("orders")
}

enum OrderStatus {
  PENDING
  PAID
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

// 订单项模型
model OrderItem {
  id        String  @id @default(cuid())
  orderId   String
  productId String
  quantity  Int
  price     Decimal @db.Decimal(10, 2)
  createdAt DateTime @default(now())

  // 关联
  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

// 玄学测试模型
model MysticalTest {
  id          String     @id @default(cuid())
  type        TestType
  name        Json       // 多语言名称
  description Json       // 多语言描述
  slug        String     @unique
  questions   Json       // 测试问题
  results     Json       // 测试结果
  duration    Int        @default(10) // 分钟
  difficulty  Difficulty @default(BEGINNER)
  featured    Boolean    @default(false)
  seoTitle    Json?      // 多语言SEO标题
  seoDescription Json?   // 多语言SEO描述
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // 关联
  sessions TestSession[]

  @@map("mystical_tests")
}

enum TestType {
  TAROT
  ASTROLOGY
  NUMEROLOGY
  CRYSTAL
  RUNE
}

enum Difficulty {
  BEGINNER
  INTERMEDIATE
  ADVANCED
}

// 测试会话模型
model TestSession {
  id          String    @id @default(cuid())
  testId      String
  userId      String?
  sessionId   String    // 匿名用户的会话ID
  answers     Json      // 用户答案
  result      Json?     // 测试结果
  completedAt DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // 关联
  test MysticalTest @relation(fields: [testId], references: [id], onDelete: Cascade)
  user User?        @relation(fields: [userId], references: [id])

  @@map("test_sessions")
}

// AI请求日志模型
model AIRequest {
  id        String    @id @default(cuid())
  provider  String    // AI提供商
  model     String    // 使用的模型
  prompt    String    // 请求提示
  response  String?   // AI响应
  tokens    Int?      // 使用的token数
  cost      Decimal?  @db.Decimal(10, 4) // 成本
  userId    String?   // 用户ID（如果有）
  sessionId String?   // 会话ID
  type      String    // 请求类型
  locale    String    // 语言
  success   Boolean   @default(true)
  error     String?   // 错误信息
  createdAt DateTime  @default(now())

  @@map("ai_requests")
}

// 网站分析模型
model Analytics {
  id          String   @id @default(cuid())
  path        String   // 页面路径
  locale      String   // 语言
  userAgent   String?  // 用户代理
  referer     String?  // 来源
  ip          String?  // IP地址
  country     String?  // 国家
  device      String?  // 设备类型
  sessionId   String   // 会话ID
  userId      String?  // 用户ID（如果有）
  duration    Int?     // 停留时间（秒）
  createdAt   DateTime @default(now())

  @@map("analytics")
}

// 邮件订阅模型
model Newsletter {
  id          String   @id @default(cuid())
  email       String   @unique
  locale      String   @default("en")
  interests   String[] // 兴趣标签
  confirmed   Boolean  @default(false)
  unsubscribed Boolean @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("newsletters")
}
