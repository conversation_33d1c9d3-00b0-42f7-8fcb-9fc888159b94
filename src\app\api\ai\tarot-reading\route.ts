import { NextRequest, NextResponse } from 'next/server';
import { aiService } from '@/lib/ai/ai-service';
import { getTarotReadingPrompt, buildPrompt } from '@/lib/ai/prompts';
import type { Locale } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { card, question, locale, type = 'daily' } = body;

    // 验证输入
    if (!card || !question || !locale) {
      return NextResponse.json(
        { error: 'Missing required fields: card, question, locale' },
        { status: 400 }
      );
    }

    // 生成提示词
    const promptTemplate = getTarotReadingPrompt(
      [card],
      question,
      locale as Locale,
      type
    );
    
    const prompt = buildPrompt(promptTemplate);

    // 调用AI服务
    const response = await aiService.sendRequest({
      type: 'tarot_reading',
      prompt,
      locale: locale as Locale,
      context: {
        card,
        question,
        type,
      },
    });

    return NextResponse.json({
      reading: response.content,
      card,
      question,
      timestamp: new Date().toISOString(),
      provider: response.provider,
    });

  } catch (error) {
    console.error('Tarot reading API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to generate tarot reading',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// 处理OPTIONS请求（CORS）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
