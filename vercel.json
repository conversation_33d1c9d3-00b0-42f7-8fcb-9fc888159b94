{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "regions": ["hkg1", "sin1", "sfo1"], "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}, {"source": "/(.*\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "redirects": [{"source": "/sitemap.xml", "destination": "/api/sitemap", "permanent": true}, {"source": "/robots.txt", "destination": "/api/robots", "permanent": true}], "rewrites": [{"source": "/health", "destination": "/api/health"}], "env": {"NEXT_PUBLIC_SITE_URL": "https://mystical-insights.vercel.app"}}